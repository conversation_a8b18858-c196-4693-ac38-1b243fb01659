<?php
session_start();
include '../../includes/config/database.php';
include '../../includes/functions/utility.php';

// Check if user is logged in, if not redirect to login page
if (!isset($_SESSION['user_id'])) {
    header("Location: ../../login.php");
    exit;
}

// Check for database connection errors
$db_error = isset($db_connection_error) && $db_connection_error;

// Initialize variables
$message = '';
$message_type = '';
$page_title = "Disaster Preparedness - Barangay Management System";

// Get disaster data counts
$active_disasters = 0;
$preparedness_count = 0;
$response_count = 0;
$recovery_count = 0;

if (!$db_error) {
    // Count active disasters
    try {
        $query = "SELECT COUNT(*) AS total FROM disaster_management WHERE status != 'Completed'";
        $stmt = $conn->query($query);
        if ($stmt) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            $active_disasters = $row['total'];
        }
        
        // Count by status
        $query = "SELECT COUNT(*) AS total FROM disaster_management WHERE status = 'Preparedness'";
        $stmt = $conn->query($query);
        if ($stmt) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            $preparedness_count = $row['total'];
        }
        
        $query = "SELECT COUNT(*) AS total FROM disaster_management WHERE status = 'Response'";
        $stmt = $conn->query($query);
        if ($stmt) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            $response_count = $row['total'];
        }
        
        $query = "SELECT COUNT(*) AS total FROM disaster_management WHERE status = 'Recovery'";
        $stmt = $conn->query($query);
        if ($stmt) {
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            $recovery_count = $row['total'];
        }
    } catch (PDOException $e) {
        error_log("Error counting disasters: " . $e->getMessage());
    }
}

// Check if there are messages stored in the session
if (isset($_SESSION['message']) && isset($_SESSION['message_type'])) {
    $message = $_SESSION['message'];
    $message_type = $_SESSION['message_type'];
    // Clear the session messages to prevent them from displaying again
    unset($_SESSION['message']);
    unset($_SESSION['message_type']);
}

// Process form submission for new disaster plan
if (isset($_POST['add_disaster'])) {
    $disaster_type = $_POST['disaster_type'];
    $disaster_date = $_POST['disaster_date'];
    $description = $_POST['description'];
    $affected_areas = $_POST['affected_areas'];
    $affected_households = $_POST['affected_households'];
    $affected_individuals = $_POST['affected_individuals'];
    $evacuees = $_POST['evacuees'];
    $casualties = $_POST['casualties'];
    $status = $_POST['status'];
    $actions_taken = $_POST['actions_taken'];
    $resources_deployed = $_POST['resources_deployed'];
    $external_assistance = $_POST['external_assistance'];
    $recorded_by = $_SESSION['user_id'];
    
    if (!$db_error) {
        $query = "INSERT INTO disaster_management (disaster_type, disaster_date, description, affected_areas, affected_households, affected_individuals, evacuees, casualties, status, 
                  actions_taken, resources_deployed, external_assistance, recorded_by)
                  VALUES (:disaster_type, :disaster_date, :description, :affected_areas, :affected_households, :affected_individuals, :evacuees, :casualties, :status, 
                  :actions_taken, :resources_deployed, :external_assistance, :recorded_by)";
        
        try {
            $stmt = $conn->prepare($query);
            $stmt->bindParam(':disaster_type', $disaster_type);
            $stmt->bindParam(':disaster_date', $disaster_date);
            $stmt->bindParam(':description', $description);
            $stmt->bindParam(':affected_areas', $affected_areas);
            $stmt->bindParam(':affected_households', $affected_households);
            $stmt->bindParam(':affected_individuals', $affected_individuals);
            $stmt->bindParam(':evacuees', $evacuees);
            $stmt->bindParam(':casualties', $casualties);
            $stmt->bindParam(':status', $status);
            $stmt->bindParam(':actions_taken', $actions_taken);
            $stmt->bindParam(':resources_deployed', $resources_deployed);
            $stmt->bindParam(':external_assistance', $external_assistance);
            $stmt->bindParam(':recorded_by', $recorded_by);
            $stmt->execute();
            
            // Log the activity
            $action_type = "Disaster Management";
            $action_details = "Added new disaster plan/response for $disaster_type";
            log_activity($conn, $recorded_by, $action_type, $action_details, 'disaster');
            
            $message = "Disaster plan/response has been successfully recorded.";
            $message_type = "success";
            
            // Redirect to prevent form resubmission on page refresh
            $_SESSION['message'] = $message;
            $_SESSION['message_type'] = $message_type;
            header("Location: " . $_SERVER['PHP_SELF']);
            exit();
        } catch(PDOException $e) {
            $message = "Error: " . $e->getMessage();
            $message_type = "danger";
        }
    }
}

// Update disaster status
if (isset($_GET['update_status']) && !empty($_GET['update_status']) && isset($_GET['status'])) {
    $disaster_id = $_GET['update_status'];
    $new_status = $_GET['status'];
    
    if (!$db_error) {
        $query = "UPDATE disaster_management SET status = :new_status WHERE disaster_id = :disaster_id";
        
        try {
            $stmt = $conn->prepare($query);
            $stmt->bindParam(':new_status', $new_status);
            $stmt->bindParam(':disaster_id', $disaster_id);
            $stmt->execute();
            
            // Log the activity
            $user_id = $_SESSION['user_id'];
            $action_type = "Status Update";
            $action_details = "Updated disaster #$disaster_id status to $new_status";
            log_activity($conn, $user_id, $action_type, $action_details, 'disaster');
            
            $message = "Disaster status has been updated to $new_status.";
            $message_type = "success";
            
            // Redirect to prevent query string parameters being retained
            $_SESSION['message'] = $message;
            $_SESSION['message_type'] = $message_type;
            header("Location: " . strtok($_SERVER['REQUEST_URI'], '?'));
            exit();
        } catch(PDOException $e) {
            $message = "Error: " . $e->getMessage();
            $message_type = "danger";
        }
    }
}

// Process form submission for editing disaster plan
if (isset($_POST['edit_disaster'])) {
    $disaster_id = $_POST['disaster_id'];
    $disaster_type = $_POST['disaster_type'];
    $disaster_date = $_POST['disaster_date'];
    $description = $_POST['description'];
    $affected_areas = $_POST['affected_areas'];
    $affected_households = $_POST['affected_households'];
    $affected_individuals = $_POST['affected_individuals'];
    $evacuees = $_POST['evacuees'];
    $casualties = $_POST['casualties'];
    $status = $_POST['status'];
    $actions_taken = $_POST['actions_taken'];
    $resources_deployed = $_POST['resources_deployed'];
    $external_assistance = $_POST['external_assistance'];
    
    if (!$db_error) {
        $query = "UPDATE disaster_management SET 
                  disaster_type = :disaster_type, 
                  disaster_date = :disaster_date, 
                  description = :description, 
                  affected_areas = :affected_areas, 
                  affected_households = :affected_households,
                  affected_individuals = :affected_individuals,
                  evacuees = :evacuees,
                  casualties = :casualties,
                  status = :status, 
                  actions_taken = :actions_taken, 
                  resources_deployed = :resources_deployed,
                  external_assistance = :external_assistance
                  WHERE disaster_id = :disaster_id";
        
        try {
            $stmt = $conn->prepare($query);
            $stmt->bindParam(':disaster_type', $disaster_type);
            $stmt->bindParam(':disaster_date', $disaster_date);
            $stmt->bindParam(':description', $description);
            $stmt->bindParam(':affected_areas', $affected_areas);
            $stmt->bindParam(':affected_households', $affected_households);
            $stmt->bindParam(':affected_individuals', $affected_individuals);
            $stmt->bindParam(':evacuees', $evacuees);
            $stmt->bindParam(':casualties', $casualties);
            $stmt->bindParam(':status', $status);
            $stmt->bindParam(':actions_taken', $actions_taken);
            $stmt->bindParam(':resources_deployed', $resources_deployed);
            $stmt->bindParam(':external_assistance', $external_assistance);
            $stmt->bindParam(':disaster_id', $disaster_id);
            $stmt->execute();
            
            // Log the activity
            $user_id = $_SESSION['user_id'];
            $action_type = "Disaster Management";
            $action_details = "Updated disaster plan/response #$disaster_id: $disaster_type";
            log_activity($conn, $user_id, $action_type, $action_details, 'disaster');
            
            $message = "Disaster plan/response has been successfully updated.";
            $message_type = "success";
            
            // Redirect to prevent form resubmission on page refresh
            $_SESSION['message'] = $message;
            $_SESSION['message_type'] = $message_type;
            header("Location: " . $_SERVER['PHP_SELF']);
            exit();
        } catch(PDOException $e) {
            $message = "Error: " . $e->getMessage();
            $message_type = "danger";
        }
    }
}

// Handle delete disaster
if (isset($_POST['delete_disaster'])) {
    try {
        $disaster_id = (int)$_POST['disaster_id'];

        // Get disaster details for logging before deletion
        $disaster_query = "SELECT * FROM disaster_management WHERE disaster_id = ?";
        $disaster_stmt = $conn->prepare($disaster_query);
        $disaster_stmt->execute([$disaster_id]);
        $disaster_info = $disaster_stmt->fetch(PDO::FETCH_ASSOC);

        if ($disaster_info) {
            // Delete the disaster record
            $delete_query = "DELETE FROM disaster_management WHERE disaster_id = ?";
            $delete_stmt = $conn->prepare($delete_query);
            $delete_stmt->execute([$disaster_id]);

            // Log the activity
            $user_id = $_SESSION['user_id'];
            $action_type = "Disaster Management";
            $action_details = "Deleted disaster record #$disaster_id: {$disaster_info['disaster_type']} on " . date('M d, Y', strtotime($disaster_info['disaster_date']));
            log_activity($conn, $user_id, $action_type, $action_details, 'disaster', $disaster_id);

            $message = "Disaster record has been successfully deleted.";
            $message_type = "success";
        } else {
            $message = "Disaster record not found.";
            $message_type = "warning";
        }

        // Redirect to prevent form resubmission
        $_SESSION['message'] = $message;
        $_SESSION['message_type'] = $message_type;
        header("Location: " . $_SERVER['PHP_SELF']);
        exit();
    } catch (PDOException $e) {
        $message = "Error deleting disaster record: " . $e->getMessage();
        $message_type = "danger";
    }
}

// Get all disaster records
$disasters = array();
if (!$db_error) {
    try {
        $query = "SELECT d.*, u.username as recorder_name
                FROM disaster_management d
                LEFT JOIN users u ON d.recorded_by = u.user_id
                ORDER BY d.date_recorded DESC";
        
        $stmt = $conn->query($query);
        if ($stmt) {
            $disasters = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
    } catch(PDOException $e) {
        error_log("Error retrieving disasters: " . $e->getMessage());
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>

    <!-- Favicon -->
    <link rel="icon" href="<?php echo get_favicon_url($conn, '../../'); ?>" type="image/png">

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="../../assets/css/style.css">
    <style>
        /* Custom styles for disaster management table */
        @media (max-width: 767px) {
            .d-flex.gap-1 {
                flex-direction: row;
                flex-wrap: wrap;
                justify-content: center;
            }
            
            .table-responsive {
                overflow-x: auto;
            }
            
            .modal-dialog {
                margin: 0.5rem;
            }
            
            .badge {
                display: inline-block;
                width: 100%;
                text-align: center;
            }
        }
        
        /* Make action buttons more visible */
        .btn-sm {
            padding: 0.25rem 0.5rem;
        }
        
        /* Improve tooltip appearance */
        .tooltip-inner {
            max-width: 300px;
            padding: 0.5rem;
            background-color: rgba(0, 0, 0, 0.9);
            border-radius: 0.25rem;
            text-align: left;
        }
        
        /* Make table cells more readable */
        #disasterTable td {
            vertical-align: middle;
        }
        
        /* Custom styling for status badges */
        .badge {
            font-size: 0.85rem;
            padding: 0.35rem 0.5rem;
        }
        
        /* Enhanced modal styling */
        .disaster-info {
            border-left: 3px solid #4e73df;
            padding-left: 15px;
            margin-bottom: 15px;
        }
        
        .disaster-info strong {
            color: #4e73df;
        }
        
        .detail-section {
            background-color: #f8f9fc;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        }
        
        .detail-section h6 {
            border-bottom: 1px solid #e3e6f0;
            padding-bottom: 10px;
            margin-bottom: 15px;
            color: #4e73df;
        }
        
        .detail-content {
            padding: 0 10px;
        }
        
        .emoji-label {
            margin-right: 10px;
            font-size: 1.3rem;
        }
        
        .info-label {
            font-weight: 600;
            color: #5a5c69;
        }
        
        /* Form enhancement */
        .form-label {
            font-weight: 500;
            color: #5a5c69;
        }
        
        .form-control:focus, .form-select:focus {
            border-color: #bac8f3;
            box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
        }
        
        .section-divider {
            height: 1px;
            background-color: #e3e6f0;
            margin: 25px 0;
        }
        
        /* Stats Card Styling */
        .stat-card {
            border-radius: 0.75rem;
            overflow: hidden;
            height: 100%;
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
            position: relative;
            z-index: 1;
        }
        
        .stat-card:hover {
            transform: translateY(-5px) !important;
            box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2) !important;
            z-index: 2;
            cursor: pointer;
        }
        
        .stat-card:hover .stat-icon {
            transform: scale(1.1);
        }
        
        .stat-icon {
            font-size: 2.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 60px;
            width: 60px;
            border-radius: 50%;
            transition: transform 0.3s ease;
        }
        
        .border-primary { border-left: 4px solid #4e73df !important; }
        .border-success { border-left: 4px solid #1cc88a !important; }
        .border-info { border-left: 4px solid #36b9cc !important; }
        .border-warning { border-left: 4px solid #f6c23e !important; }
        .border-danger { border-left: 4px solid #e74a3b !important; }
        
        .bg-primary-soft { background-color: rgba(78, 115, 223, 0.1) !important; }
        .bg-success-soft { background-color: rgba(28, 200, 138, 0.1) !important; }
        .bg-info-soft { background-color: rgba(54, 185, 204, 0.1) !important; }
        .bg-warning-soft { background-color: rgba(246, 194, 62, 0.1) !important; }
        .bg-danger-soft { background-color: rgba(231, 74, 59, 0.1) !important; }
        
        .content-card {
            border-radius: 15px;
            transition: all 0.3s ease;
            overflow: hidden;
        }
        
        .content-card:hover {
            transform: translateY(-7px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1) !important;
        }
        
        /* Toast notification styles */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
        }
        
        .toast {
            background-color: white;
            width: 350px;
            max-width: 100%;
            font-size: 0.875rem;
            pointer-events: auto;
            background-clip: padding-box;
            border: 1px solid rgba(0, 0, 0, 0.1);
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
            border-radius: 0.25rem;
            opacity: 0;
            transition: transform .15s ease-in-out, opacity .15s ease-in-out;
            transform: translateY(-100%);
        }
        
        .toast.showing {
            opacity: 1;
            transform: translateY(0);
        }
        
        .toast.show {
            display: block;
            opacity: 1;
            transform: translateY(0);
        }
        
        .toast.hide {
            display: none;
        }
        
        .toast-header {
            display: flex;
            align-items: center;
            padding: 0.5rem 0.75rem;
            color: #6c757d;
            background-color: rgba(255, 255, 255, 0.85);
            background-clip: padding-box;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            border-top-left-radius: calc(0.25rem - 1px);
            border-top-right-radius: calc(0.25rem - 1px);
        }
        
        .toast-body {
            padding: 0.75rem;
        }
        
        .me-auto {
            margin-right: auto !important;
        }

        /* Improved dropdown positioning */
        .btn-group .dropdown-menu {
            position: absolute !important;
            z-index: 1050 !important;
        }

        /* Ensure dropdown stays within viewport */
        .btn-group.dropup .dropdown-menu {
            bottom: 100% !important;
            top: auto !important;
            margin-bottom: 2px !important;
        }

        /* For tables, ensure dropdown doesn't get cut off */
        .table-responsive {
            overflow: visible !important;
        }

        /* Status dropdown specific styles */
        .status-dropdown {
            position: relative !important;
        }

        .status-dropdown .dropdown-menu {
            transform: none !important;
            right: 0 !important;
            left: auto !important;
        }

        /* Responsive dropdown positioning */
        @media (max-width: 768px) {
            .btn-group .dropdown-menu {
                position: fixed !important;
                top: 50% !important;
                left: 50% !important;
                transform: translate(-50%, -50%) !important;
                z-index: 1060 !important;
                max-width: 90vw !important;
            }
        }

        /* Status change modal styles */
        .status-option {
            transition: all 0.2s ease;
            cursor: pointer;
        }

        .status-option:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
        }

        .status-option.border-success {
            border-color: #198754 !important;
            background-color: #f8fff9 !important;
        }

        .status-icon-large {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include '../../includes/sidebar.php'; ?>
            
            <!-- Main Content -->
            <main class="col-md-8 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">🛡️ Disaster Preparedness</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addDisasterModal">
                            <i class="fas fa-plus"></i> New Disaster Plan
                        </button>
                    </div>
                </div>

                <?php if ($db_error): ?>
                <div class="alert alert-danger">
                    <strong>Database Error:</strong> Could not connect to the database. Please contact the system administrator.
                </div>
                <?php else: ?>

                <!-- Display Messages -->
                <?php /* if (!empty($message)): ?>
                <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; */ ?>

                <!-- Disaster Statistics Cards -->
                <div class="row mt-4">
                    <!-- Active Disasters -->
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card border-danger">
                            <div class="card-body p-3">
                                <div class="row align-items-center">
                                    <div class="col-4">
                                        <div class="stat-icon bg-danger-soft text-danger">
                                            🚨
                                        </div>
                                    </div>
                                    <div class="col-8 text-end">
                                        <h4 class="mt-0 mb-1"><?php echo number_format($active_disasters); ?></h4>
                                        <p class="mb-0 text-muted">Active Disasters</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Preparedness Plans -->
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card border-warning">
                            <div class="card-body p-3">
                                <div class="row align-items-center">
                                    <div class="col-4">
                                        <div class="stat-icon bg-warning-soft text-warning">
                                            ⚠️
                                        </div>
                                    </div>
                                    <div class="col-8 text-end">
                                        <h4 class="mt-0 mb-1"><?php echo number_format($preparedness_count); ?></h4>
                                        <p class="mb-0 text-muted">Preparedness Plans</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Response Operations -->
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card border-primary">
                            <div class="card-body p-3">
                                <div class="row align-items-center">
                                    <div class="col-4">
                                        <div class="stat-icon bg-primary-soft text-primary">
                                            🛠️
                                        </div>
                                    </div>
                                    <div class="col-8 text-end">
                                        <h4 class="mt-0 mb-1"><?php echo number_format($response_count); ?></h4>
                                        <p class="mb-0 text-muted">Response Operations</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Recovery Projects -->
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card border-success">
                            <div class="card-body p-3">
                                <div class="row align-items-center">
                                    <div class="col-4">
                                        <div class="stat-icon bg-success-soft text-success">
                                            🔄
                                        </div>
                                    </div>
                                    <div class="col-8 text-end">
                                        <h4 class="mt-0 mb-1"><?php echo number_format($recovery_count); ?></h4>
                                        <p class="mb-0 text-muted">Recovery Projects</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Disaster Management Table -->
                <div class="card shadow mb-4 content-card">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">🌪️ Disaster Management Plans & Activities</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered table-hover table-bordered-columns" id="disasterTable" width="100%" cellspacing="0">
                                <thead class="table-light">
                                    <tr>
                                        <th width="5%">🔢 ID</th>
                                        <th width="10%">🌪️ Disaster Type</th>
                                        <th width="8%">📅 Date</th>
                                        <th width="15%">📝 Description</th>
                                        <th width="10%">🌍 Affected Areas</th>
                                        <th width="7%">🏠 Households</th>
                                        <th width="7%">👥 Individuals</th>
                                        <th width="5%">⛺ Evacuees</th>
                                        <th width="5%">⚠️ Casualties</th>
                                        <th width="8%">🚦 Status</th>
                                        <th width="10%">🛠️ Actions Taken</th>
                                        <th width="10%">🤝 External Help</th>
                                        <th width="10%">⚙️ Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($disasters as $disaster): ?>
                                    <tr>
                                        <td><?php echo $disaster['disaster_id']; ?></td>
                                        <td><?php echo htmlspecialchars($disaster['disaster_type']); ?></td>
                                        <td><?php echo isset($disaster['disaster_date']) ? date('M d, Y', strtotime($disaster['disaster_date'])) : 'N/A'; ?></td>
                                        <td data-bs-toggle="tooltip" title="<?php echo htmlspecialchars($disaster['description']); ?>">
                                            <?php echo mb_substr(htmlspecialchars($disaster['description']), 0, 50) . (strlen($disaster['description']) > 50 ? '...' : ''); ?>
                                        </td>
                                        <td data-bs-toggle="tooltip" title="<?php echo htmlspecialchars($disaster['affected_areas']); ?>">
                                            <?php echo mb_substr(htmlspecialchars($disaster['affected_areas']), 0, 30) . (strlen($disaster['affected_areas']) > 30 ? '...' : ''); ?>
                                        </td>
                                        <td><?php echo number_format($disaster['affected_households'] ?? 0); ?></td>
                                        <td><?php echo number_format($disaster['affected_individuals'] ?? 0); ?></td>
                                        <td><?php echo number_format($disaster['evacuees'] ?? 0); ?></td>
                                        <td><?php echo number_format($disaster['casualties'] ?? 0); ?></td>
                                        <td>
                                            <span class="badge bg-<?php 
                                                echo ($disaster['status'] == 'Preparedness') ? 'warning' : 
                                                    (($disaster['status'] == 'Response') ? 'danger' : 
                                                    (($disaster['status'] == 'Recovery') ? 'primary' : 
                                                    (($disaster['status'] == 'Completed') ? 'success' : 'secondary'))); 
                                            ?>">
                                                <?php echo $disaster['status']; ?>
                                            </span>
                                        </td>
                                        <td data-bs-toggle="tooltip" title="<?php echo htmlspecialchars($disaster['actions_taken']); ?>">
                                            <?php echo mb_substr(htmlspecialchars($disaster['actions_taken']), 0, 50) . (strlen($disaster['actions_taken']) > 50 ? '...' : ''); ?>
                                        </td>
                                        <td data-bs-toggle="tooltip" title="<?php echo htmlspecialchars($disaster['external_assistance'] ?? ''); ?>">
                                            <?php echo mb_substr(htmlspecialchars($disaster['external_assistance'] ?? ''), 0, 50) . (strlen($disaster['external_assistance'] ?? '') > 50 ? '...' : ''); ?>
                                        </td>
                                        <td>
                                            <div class="d-flex gap-1 justify-content-center">
                                                <button type="button" class="btn btn-sm btn-info" data-bs-toggle="modal" data-bs-target="#viewDisasterModal<?php echo $disaster['disaster_id']; ?>" title="View Details">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#editDisasterModal<?php echo $disaster['disaster_id']; ?>" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <div class="btn-group status-dropdown">
                                                    <button type="button" class="btn btn-sm btn-warning" data-bs-toggle="modal" data-bs-target="#statusChangeModal" title="Change Status"
                                                            data-disaster-id="<?php echo $disaster['disaster_id']; ?>"
                                                            data-disaster-name="<?php echo htmlspecialchars($disaster['disaster_name']); ?>"
                                                            data-current-status="<?php echo $disaster['status']; ?>">
                                                        <i class="fas fa-flag"></i> Status
                                                    </button>
                                                </div>

                                                </div>
                                                <button type="button" class="btn btn-sm btn-danger delete-disaster" data-id="<?php echo $disaster['disaster_id']; ?>" data-type="<?php echo htmlspecialchars($disaster['disaster_type']); ?>" data-date="<?php echo date('M d, Y', strtotime($disaster['disaster_date'])); ?>" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </main>
        </div>
    </div>

    <!-- Add Disaster Modal -->
    <div class="modal fade" id="addDisasterModal" tabindex="-1" aria-labelledby="addDisasterModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title" id="addDisasterModalLabel"><i class="fas fa-plus me-2"></i>Add New Disaster Plan/Response</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form action="" method="post">
                    <div class="modal-body">
                        <div class="detail-section">
                            <h6 class="fw-bold"><i class="fas fa-info-circle me-2"></i>Basic Information</h6>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="disaster_type" class="form-label">
                                        <span class="emoji-label">🌪️</span>Disaster Type
                                    </label>
                                    <input type="text" class="form-control" id="disaster_type" name="disaster_type" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="disaster_date" class="form-label">
                                        <span class="emoji-label">📅</span>Disaster Date
                                    </label>
                                    <input type="date" class="form-control" id="disaster_date" name="disaster_date" value="<?php echo date('Y-m-d'); ?>" required>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="status" class="form-label">
                                        <span class="emoji-label">🚨</span>Status
                                    </label>
                                    <select class="form-select" id="status" name="status" required>
                                        <option value="Preparedness">Preparedness</option>
                                        <option value="Response">Response</option>
                                        <option value="Recovery">Recovery</option>
                                        <option value="Completed">Completed</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="affected_areas" class="form-label">
                                        <span class="emoji-label">🌍</span>Affected Areas
                                    </label>
                                    <input type="text" class="form-control" id="affected_areas" name="affected_areas" required>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="description" class="form-label">
                                    <span class="emoji-label">📝</span>Description
                                </label>
                                <textarea class="form-control" id="description" name="description" rows="3" required></textarea>
                            </div>
                        </div>
                        
                        <div class="section-divider"></div>
                        
                        <div class="detail-section">
                            <h6 class="fw-bold"><i class="fas fa-chart-bar me-2"></i>Impact Statistics</h6>
                            <div class="row mb-3">
                                <div class="col-md-3">
                                    <label for="affected_households" class="form-label">
                                        <span class="emoji-label">🏠</span>Households
                                    </label>
                                    <input type="number" class="form-control" id="affected_households" name="affected_households" min="0" value="0">
                                </div>
                                <div class="col-md-3">
                                    <label for="affected_individuals" class="form-label">
                                        <span class="emoji-label">👥</span>Individuals
                                    </label>
                                    <input type="number" class="form-control" id="affected_individuals" name="affected_individuals" min="0" value="0">
                                </div>
                                <div class="col-md-3">
                                    <label for="evacuees" class="form-label">
                                        <span class="emoji-label">🏕️</span>Evacuees
                                    </label>
                                    <input type="number" class="form-control" id="evacuees" name="evacuees" min="0" value="0">
                                </div>
                                <div class="col-md-3">
                                    <label for="casualties" class="form-label">
                                        <span class="emoji-label">💔</span>Casualties
                                    </label>
                                    <input type="number" class="form-control" id="casualties" name="casualties" min="0" value="0">
                                </div>
                            </div>
                        </div>
                        
                        <div class="section-divider"></div>
                        
                        <div class="detail-section">
                            <h6 class="fw-bold"><i class="fas fa-hands-helping me-2"></i>Response & Assistance</h6>
                            <div class="mb-3">
                                <label for="actions_taken" class="form-label">
                                    <span class="emoji-label">🛠️</span>Actions Taken
                                </label>
                                <textarea class="form-control" id="actions_taken" name="actions_taken" rows="3" required></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="resources_deployed" class="form-label">
                                    <span class="emoji-label">🚒</span>Resources Deployed
                                </label>
                                <textarea class="form-control" id="resources_deployed" name="resources_deployed" rows="3" required></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="external_assistance" class="form-label">
                                    <span class="emoji-label">🤝</span>External Assistance
                                </label>
                                <textarea class="form-control" id="external_assistance" name="external_assistance" rows="3"></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="add_disaster" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>Save
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- View/Edit Disaster Modals - These would be generated for each disaster -->
    <?php foreach ($disasters as $disaster): ?>
    <!-- View Disaster Modal -->
    <div class="modal fade" id="viewDisasterModal<?php echo $disaster['disaster_id']; ?>" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title"><i class="fas fa-info-circle me-2"></i>Disaster Details</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="detail-section">
                                <h6 class="fw-bold"><i class="fas fa-clipboard me-2"></i>Basic Information</h6>
                                <div class="row">
                                    <div class="col-md-6">
                                        <p><span class="emoji-label">🆔</span><span class="info-label">ID:</span> <?php echo $disaster['disaster_id']; ?></p>
                                        <p><span class="emoji-label">🌪️</span><span class="info-label">Disaster Type:</span> <?php echo htmlspecialchars($disaster['disaster_type']); ?></p>
                                        <p><span class="emoji-label">📅</span><span class="info-label">Disaster Date:</span> <?php echo isset($disaster['disaster_date']) ? date('M d, Y', strtotime($disaster['disaster_date'])) : 'Not specified'; ?></p>
                                        <p>
                                            <span class="emoji-label">🚨</span><span class="info-label">Status:</span> 
                                            <span class="badge bg-<?php 
                                                echo ($disaster['status'] == 'Preparedness') ? 'warning' : 
                                                    (($disaster['status'] == 'Response') ? 'danger' : 
                                                    (($disaster['status'] == 'Recovery') ? 'primary' : 
                                                    (($disaster['status'] == 'Completed') ? 'success' : 'secondary'))); 
                                            ?>"><?php echo $disaster['status']; ?></span>
                                        </p>
                                    </div>
                                    <div class="col-md-6">
                                        <p><span class="emoji-label">👤</span><span class="info-label">Recorded By:</span> <?php echo htmlspecialchars($disaster['recorder_name']); ?></p>
                                        <p><span class="emoji-label">⏱️</span><span class="info-label">Date Recorded:</span> <?php echo date('M d, Y h:i A', strtotime($disaster['date_recorded'])); ?></p>
                                        <p><span class="emoji-label">🌍</span><span class="info-label">Affected Areas:</span> <?php echo htmlspecialchars($disaster['affected_areas']); ?></p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="detail-section">
                                <h6 class="fw-bold"><i class="fas fa-chart-bar me-2"></i>Impact Statistics</h6>
                                <div class="row">
                                    <div class="col-md-3 col-6 mb-3">
                                        <div class="text-center">
                                            <div class="emoji-label fs-2">🏠</div>
                                            <div class="info-label">Households</div>
                                            <div class="fs-4"><?php echo number_format($disaster['affected_households'] ?? 0); ?></div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-6 mb-3">
                                        <div class="text-center">
                                            <div class="emoji-label fs-2">👥</div>
                                            <div class="info-label">Individuals</div>
                                            <div class="fs-4"><?php echo number_format($disaster['affected_individuals'] ?? 0); ?></div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-6">
                                        <div class="text-center">
                                            <div class="emoji-label fs-2">🏕️</div>
                                            <div class="info-label">Evacuees</div>
                                            <div class="fs-4"><?php echo number_format($disaster['evacuees'] ?? 0); ?></div>
                                        </div>
                                    </div>
                                    <div class="col-md-3 col-6">
                                        <div class="text-center">
                                            <div class="emoji-label fs-2">💔</div>
                                            <div class="info-label">Casualties</div>
                                            <div class="fs-4"><?php echo number_format($disaster['casualties'] ?? 0); ?></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-12">
                            <div class="detail-section">
                                <h6 class="fw-bold"><i class="fas fa-file-alt me-2"></i>Details & Actions</h6>
                                <div class="detail-content mb-3">
                                    <p><span class="emoji-label">📝</span><span class="info-label">Description:</span></p>
                                    <p class="ps-4 bg-light p-2 rounded"><?php echo nl2br(htmlspecialchars($disaster['description'])); ?></p>
                                </div>
                                
                                <div class="detail-content mb-3">
                                    <p><span class="emoji-label">🛠️</span><span class="info-label">Actions Taken:</span></p>
                                    <p class="ps-4 bg-light p-2 rounded"><?php echo nl2br(htmlspecialchars($disaster['actions_taken'])); ?></p>
                                </div>
                                
                                <div class="detail-content mb-3">
                                    <p><span class="emoji-label">🚒</span><span class="info-label">Resources Deployed:</span></p>
                                    <p class="ps-4 bg-light p-2 rounded"><?php echo nl2br(htmlspecialchars($disaster['resources_deployed'])); ?></p>
                                </div>
                                
                                <div class="detail-content">
                                    <p><span class="emoji-label">🤝</span><span class="info-label">External Assistance:</span></p>
                                    <p class="ps-4 bg-light p-2 rounded"><?php echo !empty($disaster['external_assistance']) ? nl2br(htmlspecialchars($disaster['external_assistance'])) : 'None'; ?></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#editDisasterModal<?php echo $disaster['disaster_id']; ?>">
                        <i class="fas fa-edit me-1"></i>Edit
                    </button>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Disaster Modal -->
    <div class="modal fade" id="editDisasterModal<?php echo $disaster['disaster_id']; ?>" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title"><i class="fas fa-edit me-2"></i>Edit Disaster Plan/Response</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form action="" method="post">
                    <input type="hidden" name="disaster_id" value="<?php echo $disaster['disaster_id']; ?>">
                    <div class="modal-body">
                        <div class="detail-section">
                            <h6 class="fw-bold"><i class="fas fa-info-circle me-2"></i>Basic Information</h6>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="edit_disaster_type<?php echo $disaster['disaster_id']; ?>" class="form-label">
                                        <span class="emoji-label">🌪️</span>Disaster Type
                                    </label>
                                    <input type="text" class="form-control" id="edit_disaster_type<?php echo $disaster['disaster_id']; ?>" name="disaster_type" value="<?php echo htmlspecialchars($disaster['disaster_type']); ?>" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="edit_disaster_date<?php echo $disaster['disaster_id']; ?>" class="form-label">
                                        <span class="emoji-label">📅</span>Disaster Date
                                    </label>
                                    <input type="date" class="form-control" id="edit_disaster_date<?php echo $disaster['disaster_id']; ?>" name="disaster_date" value="<?php echo date('Y-m-d', strtotime($disaster['disaster_date'] ?? date('Y-m-d'))); ?>" required>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="edit_status<?php echo $disaster['disaster_id']; ?>" class="form-label">
                                        <span class="emoji-label">🚨</span>Status
                                    </label>
                                    <select class="form-select" id="edit_status<?php echo $disaster['disaster_id']; ?>" name="status" required>
                                        <option value="Preparedness" <?php echo ($disaster['status'] == 'Preparedness') ? 'selected' : ''; ?>>Preparedness</option>
                                        <option value="Response" <?php echo ($disaster['status'] == 'Response') ? 'selected' : ''; ?>>Response</option>
                                        <option value="Recovery" <?php echo ($disaster['status'] == 'Recovery') ? 'selected' : ''; ?>>Recovery</option>
                                        <option value="Completed" <?php echo ($disaster['status'] == 'Completed') ? 'selected' : ''; ?>>Completed</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="edit_affected_areas<?php echo $disaster['disaster_id']; ?>" class="form-label">
                                        <span class="emoji-label">🌍</span>Affected Areas
                                    </label>
                                    <input type="text" class="form-control" id="edit_affected_areas<?php echo $disaster['disaster_id']; ?>" name="affected_areas" value="<?php echo htmlspecialchars($disaster['affected_areas']); ?>" required>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label for="edit_description<?php echo $disaster['disaster_id']; ?>" class="form-label">
                                    <span class="emoji-label">📝</span>Description
                                </label>
                                <textarea class="form-control" id="edit_description<?php echo $disaster['disaster_id']; ?>" name="description" rows="3" required><?php echo htmlspecialchars($disaster['description']); ?></textarea>
                            </div>
                        </div>
                        
                        <div class="section-divider"></div>
                        
                        <div class="detail-section">
                            <h6 class="fw-bold"><i class="fas fa-chart-bar me-2"></i>Impact Statistics</h6>
                            <div class="row mb-3">
                                <div class="col-md-3">
                                    <label for="edit_affected_households<?php echo $disaster['disaster_id']; ?>" class="form-label">
                                        <span class="emoji-label">🏠</span>Households
                                    </label>
                                    <input type="number" class="form-control" id="edit_affected_households<?php echo $disaster['disaster_id']; ?>" name="affected_households" min="0" value="<?php echo $disaster['affected_households'] ?? 0; ?>">
                                </div>
                                <div class="col-md-3">
                                    <label for="edit_affected_individuals<?php echo $disaster['disaster_id']; ?>" class="form-label">
                                        <span class="emoji-label">👥</span>Individuals
                                    </label>
                                    <input type="number" class="form-control" id="edit_affected_individuals<?php echo $disaster['disaster_id']; ?>" name="affected_individuals" min="0" value="<?php echo $disaster['affected_individuals'] ?? 0; ?>">
                                </div>
                                <div class="col-md-3">
                                    <label for="edit_evacuees<?php echo $disaster['disaster_id']; ?>" class="form-label">
                                        <span class="emoji-label">🏕️</span>Evacuees
                                    </label>
                                    <input type="number" class="form-control" id="edit_evacuees<?php echo $disaster['disaster_id']; ?>" name="evacuees" min="0" value="<?php echo $disaster['evacuees'] ?? 0; ?>">
                                </div>
                                <div class="col-md-3">
                                    <label for="edit_casualties<?php echo $disaster['disaster_id']; ?>" class="form-label">
                                        <span class="emoji-label">💔</span>Casualties
                                    </label>
                                    <input type="number" class="form-control" id="edit_casualties<?php echo $disaster['disaster_id']; ?>" name="casualties" min="0" value="<?php echo $disaster['casualties'] ?? 0; ?>">
                                </div>
                            </div>
                        </div>
                        
                        <div class="section-divider"></div>
                        
                        <div class="detail-section">
                            <h6 class="fw-bold"><i class="fas fa-hands-helping me-2"></i>Response & Assistance</h6>
                            <div class="mb-3">
                                <label for="edit_actions_taken<?php echo $disaster['disaster_id']; ?>" class="form-label">
                                    <span class="emoji-label">🛠️</span>Actions Taken
                                </label>
                                <textarea class="form-control" id="edit_actions_taken<?php echo $disaster['disaster_id']; ?>" name="actions_taken" rows="3" required><?php echo htmlspecialchars($disaster['actions_taken']); ?></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="edit_resources_deployed<?php echo $disaster['disaster_id']; ?>" class="form-label">
                                    <span class="emoji-label">🚒</span>Resources Deployed
                                </label>
                                <textarea class="form-control" id="edit_resources_deployed<?php echo $disaster['disaster_id']; ?>" name="resources_deployed" rows="3" required><?php echo htmlspecialchars($disaster['resources_deployed']); ?></textarea>
                            </div>
                            <div class="mb-3">
                                <label for="edit_external_assistance<?php echo $disaster['disaster_id']; ?>" class="form-label">
                                    <span class="emoji-label">🤝</span>External Assistance
                                </label>
                                <textarea class="form-control" id="edit_external_assistance<?php echo $disaster['disaster_id']; ?>" name="external_assistance" rows="3"><?php echo htmlspecialchars($disaster['external_assistance'] ?? ''); ?></textarea>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="edit_disaster" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>Update
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php endforeach; ?>

    <!-- Include jQuery, Bootstrap JS, and DataTables JS -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // Handle status change modal
            $('#statusChangeModal').on('show.bs.modal', function (event) {
                const button = $(event.relatedTarget);
                const disasterId = button.data('disaster-id');
                const disasterName = button.data('disaster-name');
                const currentStatus = button.data('current-status');

                // Update modal content
                $('#statusDisasterName').text(disasterName);
                $('#statusDisasterId').val(disasterId);

                // Update current status badge
                const statusBadge = $('#currentStatusBadge');
                statusBadge.removeClass('bg-warning bg-danger bg-primary bg-success');

                let badgeClass = 'bg-secondary';
                let statusText = currentStatus;

                switch(currentStatus) {
                    case 'Preparedness':
                        badgeClass = 'bg-warning text-dark';
                        statusText = '⚠️ Preparedness';
                        break;
                    case 'Response':
                        badgeClass = 'bg-danger';
                        statusText = '🚨 Response';
                        break;
                    case 'Recovery':
                        badgeClass = 'bg-primary';
                        statusText = '🔄 Recovery';
                        break;
                    case 'Completed':
                        badgeClass = 'bg-success';
                        statusText = '✅ Completed';
                        break;
                }

                statusBadge.addClass(badgeClass).text(statusText);

                // Clear any previously selected radio buttons
                $('input[name="status"]').prop('checked', false);

                // Highlight status options on hover
                $('.status-option').hover(
                    function() {
                        $(this).addClass('border-primary bg-light');
                    },
                    function() {
                        if (!$(this).find('input').is(':checked')) {
                            $(this).removeClass('border-primary bg-light');
                        }
                    }
                );

                // Handle radio button selection
                $('input[name="status"]').change(function() {
                    $('.status-option').removeClass('border-primary bg-light border-success');
                    if ($(this).is(':checked')) {
                        $(this).closest('.status-option').addClass('border-success bg-light');
                    }
                });
            });

            $('#disasterTable').DataTable({
                "order": [[0, "desc"]],
                "responsive": true,
                "columnDefs": [
                    { "width": "5%", "targets": 0 },   // ID column
                    { "width": "10%", "targets": 1 },  // Disaster Type
                    { "width": "8%", "targets": 2 },   // Date
                    { "width": "15%", "targets": 3 },  // Description
                    { "width": "10%", "targets": 4 },  // Affected Areas
                    { "width": "7%", "targets": 5 },   // Households
                    { "width": "7%", "targets": 6 },   // Individuals
                    { "width": "5%", "targets": 7 },   // Evacuees
                    { "width": "5%", "targets": 8 },   // Casualties
                    { "width": "8%", "targets": 9 },   // Status
                    { "width": "10%", "targets": 10 }, // Actions Taken
                    { "width": "10%", "targets": 11 }, // External Assistance
                    { "width": "10%", "targets": 12, "orderable": false }  // Actions column
                ],
                "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]]
            });
            
            // Initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl, {
                    boundary: document.body
                });
            });
            
            // Status change functionality with confirmation modal
            $('.status-item').on('click', function(e) {
                e.preventDefault();
                
                const disasterId = $(this).data('id');
                const newStatus = $(this).data('status');
                const statusText = $(this).find('span:nth-child(2)').text();
                const statusEmoji = $(this).find('.status-icon').text();
                
                // Create and show the confirmation modal
                const modalId = 'statusConfirmModal' + Math.floor(Math.random() * 1000);
                const modalHtml = `
                <div class="modal fade" id="${modalId}" tabindex="-1" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header ${newStatus === 'Completed' ? 'bg-success' : (newStatus === 'Response' ? 'bg-danger' : (newStatus === 'Recovery' ? 'bg-primary' : 'bg-warning'))} text-white">
                                <h5 class="modal-title">
                                    <i class="fas fa-exchange-alt me-2"></i>Change Status
                                </h5>
                                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body py-4">
                                <div class="text-center mb-4">
                                    <span style="font-size: 3rem;">${statusEmoji}</span>
                                    <h4 class="mt-3">Update to "${statusText}" Status?</h4>
                                    <p class="text-muted">
                                        This will change the disaster management plan status to ${statusText}.
                                        ${newStatus === 'Completed' ? 'The item will be marked as resolved.' : ''}
                                    </p>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                <a href="?update_status=${disasterId}&status=${newStatus}" class="btn ${newStatus === 'Completed' ? 'btn-success' : (newStatus === 'Response' ? 'btn-danger' : (newStatus === 'Recovery' ? 'btn-primary' : 'btn-warning'))}">
                                    <i class="fas fa-check-circle me-1"></i>Confirm Change
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                `;
                
                // Add modal to body and show it
                $('body').append(modalHtml);
                const modal = new bootstrap.Modal(document.getElementById(modalId));
                modal.show();
                
                // Remove modal from DOM after it's hidden
                $(`#${modalId}`).on('hidden.bs.modal', function() {
                    $(this).remove();
                });
            });
            
            // Show toast notification if there's a message
            <?php if(!empty($message)): ?>
            // Update toast styling based on message type
            const toast = document.getElementById('successToast');
            
            <?php if($message_type == 'success'): ?>
            toast.style.backgroundColor = '#28a745';
            <?php elseif($message_type == 'danger' || $message_type == 'error'): ?>
            toast.style.backgroundColor = '#dc3545';
            <?php elseif($message_type == 'warning'): ?>
            toast.style.backgroundColor = '#ffc107';
            toast.style.color = '#000';
            <?php else: ?>
            toast.style.backgroundColor = '#17a2b8';
            <?php endif; ?>
            
            toast.style.color = <?php echo ($message_type == 'warning') ? "'#000'" : "'white'"; ?>;
            
            const successToast = new bootstrap.Toast(toast, {
                delay: 5000
            });
            successToast.show();
            <?php endif; ?>
        });
    </script>
    
    <!-- Toast container -->
    <div class="toast-container position-fixed top-0 end-0 p-3">
        <div id="successToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex align-items-center p-3">
                <div class="me-2">✅</div>
                <div class="me-auto"><?php echo $message; ?></div>
                <div>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Confirmation Modal -->
    <div class="modal fade" id="deleteDisasterModal" tabindex="-1" aria-labelledby="deleteDisasterModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="deleteDisasterModalLabel">🗑️ Delete Disaster Record</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle"></i>
                        <strong>Warning!</strong> This action cannot be undone.
                    </div>
                    <p>Are you sure you want to delete this disaster record?</p>
                    <div class="disaster-info">
                        <strong>Disaster Type:</strong> <span id="delete_disaster_type"></span><br>
                        <strong>Date:</strong> <span id="delete_disaster_date"></span>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">❌ Cancel</button>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="disaster_id" id="delete_disaster_id">
                        <button type="submit" name="delete_disaster" class="btn btn-danger">
                            🗑️ Delete Record
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Status Change Modal -->
    <div class="modal fade" id="statusChangeModal" tabindex="-1" aria-labelledby="statusChangeModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title" id="statusChangeModalLabel">
                        <i class="fas fa-exchange-alt me-2"></i>Change Disaster Status
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center mb-4">
                        <div class="status-icon-large mb-3" style="font-size: 3rem;">🌪️</div>
                        <h6 class="fw-bold" id="statusDisasterName">Disaster Name</h6>
                        <p class="text-muted mb-0">Current Status: <span id="currentStatusBadge" class="badge"></span></p>
                    </div>

                    <form method="POST" id="statusChangeForm">
                        <input type="hidden" name="disaster_id" id="statusDisasterId">
                        <input type="hidden" name="action" value="update_status">

                        <div class="mb-4">
                            <label class="form-label fw-bold">Select New Status:</label>
                            <div class="status-options">
                                <div class="form-check status-option mb-3 p-3 border rounded">
                                    <input class="form-check-input" type="radio" name="status" id="statusPreparedness" value="Preparedness">
                                    <label class="form-check-label w-100 d-flex align-items-center" for="statusPreparedness">
                                        <span class="status-icon me-3" style="font-size: 1.5rem;">⚠️</span>
                                        <div class="flex-grow-1">
                                            <div class="fw-bold">Preparedness</div>
                                            <small class="text-muted">Planning and preparation phase</small>
                                        </div>
                                        <span class="badge bg-warning text-dark">Planning</span>
                                    </label>
                                </div>

                                <div class="form-check status-option mb-3 p-3 border rounded">
                                    <input class="form-check-input" type="radio" name="status" id="statusResponse" value="Response">
                                    <label class="form-check-label w-100 d-flex align-items-center" for="statusResponse">
                                        <span class="status-icon me-3" style="font-size: 1.5rem;">🚨</span>
                                        <div class="flex-grow-1">
                                            <div class="fw-bold">Response</div>
                                            <small class="text-muted">Active emergency response</small>
                                        </div>
                                        <span class="badge bg-danger">Active</span>
                                    </label>
                                </div>

                                <div class="form-check status-option mb-3 p-3 border rounded">
                                    <input class="form-check-input" type="radio" name="status" id="statusRecovery" value="Recovery">
                                    <label class="form-check-label w-100 d-flex align-items-center" for="statusRecovery">
                                        <span class="status-icon me-3" style="font-size: 1.5rem;">🔄</span>
                                        <div class="flex-grow-1">
                                            <div class="fw-bold">Recovery</div>
                                            <small class="text-muted">Recovery and rehabilitation</small>
                                        </div>
                                        <span class="badge bg-primary">In Progress</span>
                                    </label>
                                </div>

                                <div class="form-check status-option mb-3 p-3 border rounded">
                                    <input class="form-check-input" type="radio" name="status" id="statusCompleted" value="Completed">
                                    <label class="form-check-label w-100 d-flex align-items-center" for="statusCompleted">
                                        <span class="status-icon me-3" style="font-size: 1.5rem;">✅</span>
                                        <div class="flex-grow-1">
                                            <div class="fw-bold">Completed</div>
                                            <small class="text-muted">All activities completed</small>
                                        </div>
                                        <span class="badge bg-success">Done</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="fas fa-times me-2"></i>Cancel
                    </button>
                    <button type="submit" form="statusChangeForm" class="btn btn-warning">
                        <i class="fas fa-exchange-alt me-2"></i>Update Status
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Handle delete disaster button clicks
        document.addEventListener('DOMContentLoaded', function() {
            const deleteButtons = document.querySelectorAll('.delete-disaster');
            deleteButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const disasterId = this.getAttribute('data-id');
                    const disasterType = this.getAttribute('data-type');
                    const disasterDate = this.getAttribute('data-date');

                    document.getElementById('delete_disaster_id').value = disasterId;
                    document.getElementById('delete_disaster_type').textContent = disasterType;
                    document.getElementById('delete_disaster_date').textContent = disasterDate;

                    const modal = new bootstrap.Modal(document.getElementById('deleteDisasterModal'));
                    modal.show();
                });
            });
        });
    </script>
</body>
</html>