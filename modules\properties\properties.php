<?php
// Include session management
include '../../includes/session.php';
include '../../includes/functions/functions.php';
include '../../includes/functions/log_function.php';

// Check if user is logged in
$is_logged_in = isset($_SESSION['user_id']) ? true : false;

// Check if database connection has error
if (isset($db_error) && $db_error) {
    $_SESSION['error'] = 'Database connection error occurred. Please try again later.';
    header('location: ../../index.php');
    exit();
}

// Clear any lingering error messages if this is a fresh page load (not an AJAX request)
if (!isset($_SERVER['HTTP_X_REQUESTED_WITH']) || strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) != 'xmlhttprequest') {
    if (isset($_SESSION['error'])) {
        unset($_SESSION['error']);
    }
}

// Set page title
$page_title = "Property Management - Barangay Management System";

// Initialize variables
$properties = array();
$total_properties = 0;
$total_value = 0;
$available_count = 0;
$maintenance_count = 0;

// Calculate totals
if (!$db_error) {
    try {
        // Total property count
        $count_query = "SELECT COUNT(*) as total FROM properties";
        $count_result = $conn->query($count_query);
        $total_properties = $count_result->fetch(PDO::FETCH_ASSOC)['total'];

        // Total property value
        $value_query = "SELECT SUM(acquisition_cost) as total_value FROM properties";
        $value_result = $conn->query($value_query);
        $total_value = $value_result->fetch(PDO::FETCH_ASSOC)['total_value'] ?: 0;

        // Available properties
        $available_query = "SELECT COUNT(*) as available FROM properties WHERE status = 'Available'";
        $available_result = $conn->query($available_query);
        $available_count = $available_result->fetch(PDO::FETCH_ASSOC)['available'];

        // Maintenance properties
        $maintenance_query = "SELECT COUNT(*) as maintenance FROM properties WHERE status = 'Under Maintenance'";
        $maintenance_result = $conn->query($maintenance_query);
        $maintenance_count = $maintenance_result->fetch(PDO::FETCH_ASSOC)['maintenance'];
    } catch (PDOException $e) {
        error_log("Error calculating property totals: " . $e->getMessage());
        $_SESSION['error'] = 'A database error occurred. Please try again later.';
    }
}

// Process form submission for new property
if (isset($_POST['add_property'])) {
    $property_name = $_POST['property_name'];
    $property_type = $_POST['property_type'];
    $description = $_POST['description'];
    $location = $_POST['location'];
    $acquisition_date = $_POST['acquisition_date'];
    $acquisition_cost = $_POST['acquisition_cost'];
    $status = $_POST['status'];
    $remarks = $_POST['remarks'];
    
    // Handle file upload for image if provided
    $property_image = null;
    if(isset($_FILES['property_image']) && $_FILES['property_image']['error'] == 0) {
        $uploadDir = '../../uploads/properties/';
        
        // Create directory if it doesn't exist
        if (!file_exists($uploadDir)) {
            mkdir($uploadDir, 0777, true);
        }
        
        $fileName = $_FILES['property_image']['name'];
        $fileExt = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
        
        // Generate unique filename
        $newFileName = uniqid() . '_property.' . $fileExt;
        $destination = $uploadDir . $newFileName;
        
        // Move the uploaded file
        if(move_uploaded_file($_FILES['property_image']['tmp_name'], $destination)) {
            $property_image = $newFileName;
        }
    }
    
    try {
        // Use PDO prepared statement for the properties table
        $query = "INSERT INTO properties (property_name, property_type, description, location, 
                  acquisition_date, acquisition_cost, status, property_image, remarks)
                  VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $stmt = $conn->prepare($query);
        if (!$stmt) {
            throw new PDOException("Failed to prepare statement: " . $conn->errorInfo()[2]);
        }
        
        $result = $stmt->execute([
            $property_name, $property_type, $description, $location, 
            $acquisition_date, $acquisition_cost, $status, $property_image, $remarks
        ]);
        
        if (!$result) {
            throw new PDOException("Execute failed: " . $stmt->errorInfo()[2]);
        }
        
        // Log the activity
        $action_type = "Property Record";
        $action_details = "Added new property: $property_name";
        logActivity($conn, $action_details, $_SESSION['user_id'], 'properties', $action_type);
        
        $_SESSION['message'] = "Property has been successfully recorded.";
        $_SESSION['message_type'] = "success";
        
        // Redirect to prevent form resubmission on page refresh
        header("Location: properties.php");
        exit;
    } catch (PDOException $e) {
        $message = "Error: " . $e->getMessage();
        $message_type = "danger";
    } catch (Exception $e) {
        $message = "Error: " . $e->getMessage();
        $message_type = "danger";
    }
}

// Process property deletion (only for admin)
if (isset($_GET['delete']) && !empty($_GET['delete']) && $_SESSION['user_type'] == 'Admin') {
    $property_id = $_GET['delete'];
    
    try {
        // Get property details for logging
        $get_property = "SELECT * FROM properties WHERE property_id = ?";
        $property_stmt = $conn->prepare($get_property);
        $property_stmt->execute([$property_id]);
        $property_details = $property_stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($property_details) {
            // Delete the property image if it exists
            if (!empty($property_details['property_image'])) {
                $image_path = '../../uploads/properties/' . $property_details['property_image'];
                if (file_exists($image_path)) {
                    unlink($image_path);
                }
            }
            
            // Delete the property
            $query = "DELETE FROM properties WHERE property_id = ?";
            $stmt = $conn->prepare($query);
            $stmt->execute([$property_id]);
            
            // Log the activity
            $action_type = "Property Deletion";
            $action_details = "Deleted property #$property_id: " . $property_details['property_name'];
            log_activity_safe($conn, $_SESSION['user_id'], $action_type, $action_details, 'properties', $property_id);
            
            $_SESSION['message'] = "Property has been deleted successfully.";
            $_SESSION['message_type'] = "success";
        } else {
            $_SESSION['message'] = "Property not found.";
            $_SESSION['message_type'] = "warning";
        }
        
        // Redirect to prevent resubmission
        header("Location: properties.php");
        exit;
    } catch (PDOException $e) {
        $_SESSION['message'] = "Error: " . $e->getMessage();
        $_SESSION['message_type'] = "danger";
        header("Location: properties.php");
        exit;
    }
}

// Process property editing (only for admin)
if (isset($_POST['edit_property']) && $_SESSION['user_type'] == 'Admin') {
    $property_id = $_POST['property_id'];
    $property_name = $_POST['property_name'];
    $property_type = $_POST['property_type'];
    $description = $_POST['description'];
    $location = $_POST['location'];
    $acquisition_date = $_POST['acquisition_date'];
    $acquisition_cost = $_POST['acquisition_cost'];
    $status = $_POST['status'];
    $remarks = $_POST['remarks'];
    
    try {
        // Get current property details for comparison and image path
        $get_property = "SELECT * FROM properties WHERE property_id = ?";
        $property_stmt = $conn->prepare($get_property);
        $property_stmt->execute([$property_id]);
        $current_property = $property_stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$current_property) {
            throw new Exception('Property not found.');
        }
        
        // Handle file upload for image if provided
        $property_image = $current_property['property_image']; // Default to current image
        
        // Check if remove image checkbox is checked
        if(isset($_POST['remove_image']) && $_POST['remove_image'] == 1) {
            // Delete the existing image file if it exists
            if (!empty($current_property['property_image'])) {
                $image_path = '../../uploads/properties/' . $current_property['property_image'];
                if (file_exists($image_path)) {
                    unlink($image_path);
                }
            }
            // Set property_image to NULL
            $property_image = NULL;
        }
        // Only process new upload if remove image is not checked
        else if(isset($_FILES['property_image']) && $_FILES['property_image']['error'] == 0) {
            $uploadDir = '../../uploads/properties/';
            
            // Create directory if it doesn't exist
            if (!file_exists($uploadDir)) {
                mkdir($uploadDir, 0777, true);
            }
            
            $fileName = $_FILES['property_image']['name'];
            $fileExt = strtolower(pathinfo($fileName, PATHINFO_EXTENSION));
            
            // Generate unique filename
            $newFileName = uniqid() . '_property.' . $fileExt;
            $destination = $uploadDir . $newFileName;
            
            // Move the uploaded file
            if(move_uploaded_file($_FILES['property_image']['tmp_name'], $destination)) {
                // Delete old image if exists
                if (!empty($current_property['property_image'])) {
                    $old_image_path = $uploadDir . $current_property['property_image'];
                    if (file_exists($old_image_path)) {
                        unlink($old_image_path);
                    }
                }
                
                $property_image = $newFileName;
            }
        }
        
        // Use PDO prepared statement for updating
        $query = "UPDATE properties SET 
                  property_name = ?, 
                  property_type = ?,
                  description = ?,
                  location = ?,
                  acquisition_date = ?,
                  acquisition_cost = ?,
                  status = ?,
                  property_image = ?,
                  remarks = ?,
                  last_updated = NOW()
                  WHERE property_id = ?";
        
        $stmt = $conn->prepare($query);
        $result = $stmt->execute([
            $property_name,
            $property_type,
            $description,
            $location,
            $acquisition_date,
            $acquisition_cost,
            $status,
            $property_image,
            $remarks,
            $property_id
        ]);
        
        if (!$result) {
            throw new PDOException("Failed to update property record.");
        }
        
        // Log the activity
        $action_type = "Property Update";
        $action_details = "Updated property #$property_id: $property_name";
        logActivity($conn, $action_details, $_SESSION['user_id'], 'properties', $action_type);
        
        $_SESSION['message'] = "Property has been updated successfully.";
        $_SESSION['message_type'] = "success";
        
        // Redirect to prevent form resubmission
        header("Location: properties.php");
        exit;
    } catch (PDOException $e) {
        $_SESSION['message'] = "Database error: " . $e->getMessage();
        $_SESSION['message_type'] = "danger";
        header("Location: properties.php");
        exit;
    } catch (Exception $e) {
        $_SESSION['message'] = "Error: " . $e->getMessage();
        $_SESSION['message_type'] = "danger";
        header("Location: properties.php");
        exit;
    }
}

// Get filter parameters
$filter_type = isset($_GET['filter_type']) ? $_GET['filter_type'] : '';
$filter_status = isset($_GET['filter_status']) ? $_GET['filter_status'] : '';
$search_term = isset($_GET['search']) ? $_GET['search'] : '';

// Get all properties with filtering
if (!$db_error) {
    try {
        // Build the query with filters
        $query = "SELECT * FROM properties WHERE 1=1";
        $params = [];

        // Add property type filter
        if (!empty($filter_type)) {
            $query .= " AND property_type = ?";
            $params[] = $filter_type;
        }

        // Add status filter
        if (!empty($filter_status)) {
            $query .= " AND status = ?";
            $params[] = $filter_status;
        }

        // Add search filter
        if (!empty($search_term)) {
            $query .= " AND (property_name LIKE ? OR location LIKE ? OR description LIKE ?)";
            $search_param = "%$search_term%";
            $params[] = $search_param;
            $params[] = $search_param;
            $params[] = $search_param;
        }

        $query .= " ORDER BY acquisition_date DESC, property_id DESC";

        $stmt = $conn->prepare($query);
        $stmt->execute($params);
        $properties = $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (PDOException $e) {
        $message = "Error fetching properties: " . $e->getMessage();
        $message_type = "danger";
    }
}

// Display session messages if any
if (isset($_SESSION['message']) && isset($_SESSION['message_type'])) {
    $message = $_SESSION['message'];
    $message_type = $_SESSION['message_type'];
    // Don't unset yet - will be handled by the toast notification
    // unset($_SESSION['message']);
    // unset($_SESSION['message_type']);
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>
    
    <!-- Favicon -->
    <link rel="icon" href="<?php echo get_favicon_url($conn, '../../'); ?>" type="image/png">
    
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="../../assets/css/style.css">
    <style>
        body { 
            padding: 20px; 
            background-color: #f8f9fa;
        }
        .container-fluid { 
            max-width: 1800px;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            margin-bottom: 1.5rem;
            border-radius: 0.5rem;
            overflow: hidden;
        }
        .card-header {
            font-weight: 600;
        }
        
        /* Left border colors */
        .border-left-primary { border-left: 4px solid #4e73df !important; }
        .border-left-success { border-left: 4px solid #1cc88a !important; }
        .border-left-info { border-left: 4px solid #36b9cc !important; }
        .border-left-warning { border-left: 4px solid #f6c23e !important; }
        .border-left-danger { border-left: 4px solid #e74a3b !important; }
        .border-left-secondary { border-left: 4px solid #858796 !important; }
        .border-left-purple { border-left: 4px solid #6f42c1 !important; }
        .border-left-orange { border-left: 4px solid #fd7e14 !important; }
        .border-left-teal { border-left: 4px solid #20c997 !important; }
        
        /* Text colors */
        .text-primary { color: #4e73df !important; }
        .text-success { color: #1cc88a !important; }
        .text-info { color: #36b9cc !important; }
        .text-warning { color: #f6c23e !important; }
        .text-danger { color: #e74a3b !important; }
        .text-purple { color: #6f42c1 !important; }
        .text-orange { color: #fd7e14 !important; }
        .text-teal { color: #20c997 !important; }
        
        /* Background colors with opacity */
        .bg-primary-soft { background-color: rgba(78, 115, 223, 0.1) !important; }
        .bg-success-soft { background-color: rgba(28, 200, 138, 0.1) !important; }
        .bg-info-soft { background-color: rgba(54, 185, 204, 0.1) !important; }
        .bg-warning-soft { background-color: rgba(246, 194, 62, 0.1) !important; }
        .bg-danger-soft { background-color: rgba(231, 74, 59, 0.1) !important; }
        .bg-purple-soft { background-color: rgba(111, 66, 193, 0.1) !important; }
        .bg-orange-soft { background-color: rgba(253, 126, 20, 0.1) !important; }
        .bg-teal-soft { background-color: rgba(32, 201, 151, 0.1) !important; }
        
        /* Button styles */
        .btn {
            border-radius: 0.35rem;
            padding: 0.375rem 1rem;
            transition: none; /* Remove transition for better performance */
            position: relative; /* For pseudo-element hover effect */
            overflow: hidden;
        }
        
        .btn:hover::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255,255,255,0.1);
        }
        
        /* Badge styles */
        .badge {
            font-weight: 600;
            padding: 0.4em 0.8em;
            border-radius: 0.35rem;
        }
        
        /* Stats card */
        .stat-card {
            border-radius: 0.75rem;
            overflow: hidden;
            height: 100%;
            transition: all 0.3s ease;
            border: none;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
            position: relative;
            z-index: 1;
        }
        .stat-card:hover {
            transform: translateY(-5px) !important;
            box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2) !important;
            z-index: 2;
            cursor: pointer;
        }
        .stat-card:hover .stat-icon {
            transform: scale(1.1);
        }
        .stat-icon {
            font-size: 2.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 60px;
            width: 60px;
            border-radius: 50%;
            transition: transform 0.3s ease;
        }
        
        /* Border colors for different statuses */
        .border-primary { border-left: 4px solid #4e73df !important; }
        .border-success { border-left: 4px solid #1cc88a !important; }
        .border-info { border-left: 4px solid #36b9cc !important; }
        .border-warning { border-left: 4px solid #f6c23e !important; }
        .border-danger { border-left: 4px solid #e74a3b !important; }
        
        /* Table styles */
        .table {
            border-radius: 0.5rem;
            overflow: hidden;
        }
        .table th {
            background-color: #f8f9fc;
            border-bottom-width: 1px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.85rem;
            color: #4e73df;
        }
        
        /* Modal styles */
        .modal-content {
            border-radius: 0.5rem;
            overflow: hidden;
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        
        /* Card hover effects */
        .card.shadow {
            transition: all 0.3s ease;
        }
        
        .card.shadow:hover {
            box-shadow: 0 0.5rem 1.5rem 0 rgba(58, 59, 69, 0.15) !important;
            transform: translateY(-3px);
        }
        
        /* Action buttons styling */
        .action-buttons .btn {
            margin: 0 2px;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            cursor: pointer;
        }
        
        .action-buttons i {
            color: white;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include '../../includes/sidebar.php'; ?>
            
            <!-- Main Content -->
            <main class="col-md-8 ms-sm-auto col-lg-10 px-md-0">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">🏢 Property Management</h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addPropertyModal">
                            <i class="fas fa-plus"></i> Add New Property
                        </button>
                    </div>
                </div>

                <?php /* if (isset($_SESSION['error'])): ?>
                <!-- Error Message -->
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php 
                    echo $_SESSION['error'];
                    unset($_SESSION['error']);
                    ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <?php if (isset($_SESSION['success'])): ?>
                <!-- Success Message -->
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php 
                    echo $_SESSION['success'];
                    unset($_SESSION['success']);
                    ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <?php if (isset($_SESSION['message']) && isset($_SESSION['message_type'])): ?>
                <!-- General Message -->
                <div class="alert alert-<?php echo $_SESSION['message_type']; ?> alert-dismissible fade show" role="alert">
                    <?php 
                    echo $_SESSION['message'];
                    // Don't unset yet - will be handled by the toast notification
                    // unset($_SESSION['message']);
                    // unset($_SESSION['message_type']);
                    ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; */ ?>

                <!-- Summary Cards -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card border-primary">
                            <div class="card-body p-3">
                                <div class="row align-items-center">
                                    <div class="col-4">
                                        <div class="stat-icon bg-primary-soft text-primary">
                                            🏢
                                        </div>
                                    </div>
                                    <div class="col-8 text-end">
                                        <h4 class="mt-0 mb-1"><?php echo number_format($total_properties); ?></h4>
                                        <p class="mb-0 text-muted">Total Properties</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card border-success">
                            <div class="card-body p-3">
                                <div class="row align-items-center">
                                    <div class="col-4">
                                        <div class="stat-icon bg-success-soft text-success">
                                            💰
                                        </div>
                                    </div>
                                    <div class="col-8 text-end">
                                        <h4 class="mt-0 mb-1">₱<?php echo number_format($total_value, 2); ?></h4>
                                        <p class="mb-0 text-muted">Total Value</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card border-info">
                            <div class="card-body p-3">
                                <div class="row align-items-center">
                                    <div class="col-4">
                                        <div class="stat-icon bg-info-soft text-info">
                                            ✅
                                        </div>
                                    </div>
                                    <div class="col-8 text-end">
                                        <h4 class="mt-0 mb-1"><?php echo number_format($available_count); ?></h4>
                                        <p class="mb-0 text-muted">Available Properties</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card border-warning">
                            <div class="card-body p-3">
                                <div class="row align-items-center">
                                    <div class="col-4">
                                        <div class="stat-icon bg-warning-soft text-warning">
                                            🔧
                                        </div>
                                    </div>
                                    <div class="col-8 text-end">
                                        <h4 class="mt-0 mb-1"><?php echo number_format($maintenance_count); ?></h4>
                                        <p class="mb-0 text-muted">Under Maintenance</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filter Controls -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">🔍 Filter Properties</h6>
                    </div>
                    <div class="card-body">
                        <form method="GET" action="" class="row g-3">
                            <div class="col-md-3">
                                <label for="filter_type" class="form-label">📋 Property Type</label>
                                <select class="form-select" id="filter_type" name="filter_type">
                                    <option value="">All Types</option>
                                    <option value="Building" <?php echo ($filter_type == 'Building') ? 'selected' : ''; ?>>🏢 Building</option>
                                    <option value="Land" <?php echo ($filter_type == 'Land') ? 'selected' : ''; ?>>🏞️ Land</option>
                                    <option value="Vehicle" <?php echo ($filter_type == 'Vehicle') ? 'selected' : ''; ?>>🚗 Vehicle</option>
                                    <option value="Equipment" <?php echo ($filter_type == 'Equipment') ? 'selected' : ''; ?>>🔧 Equipment</option>
                                    <option value="Furniture" <?php echo ($filter_type == 'Furniture') ? 'selected' : ''; ?>>🪑 Furniture</option>
                                    <option value="Electronics" <?php echo ($filter_type == 'Electronics') ? 'selected' : ''; ?>>💻 Electronics</option>
                                    <option value="Other" <?php echo ($filter_type == 'Other') ? 'selected' : ''; ?>>📦 Other</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="filter_status" class="form-label">🚦 Status</label>
                                <select class="form-select" id="filter_status" name="filter_status">
                                    <option value="">All Statuses</option>
                                    <option value="Available" <?php echo ($filter_status == 'Available') ? 'selected' : ''; ?>>✅ Available</option>
                                    <option value="Under Maintenance" <?php echo ($filter_status == 'Under Maintenance') ? 'selected' : ''; ?>>🔧 Under Maintenance</option>
                                    <option value="Disposed" <?php echo ($filter_status == 'Disposed') ? 'selected' : ''; ?>>🗑️ Disposed</option>
                                    <option value="Borrowed" <?php echo ($filter_status == 'Borrowed') ? 'selected' : ''; ?>>📤 Borrowed</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="search" class="form-label">🔍 Search</label>
                                <input type="text" class="form-control" id="search" name="search"
                                       placeholder="Search by name, location, or description..."
                                       value="<?php echo htmlspecialchars($search_term); ?>">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid gap-2">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search"></i> Filter
                                    </button>
                                    <a href="properties.php" class="btn btn-outline-secondary">
                                        <i class="fas fa-times"></i> Clear
                                    </a>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Properties Table -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center">
                        <h6 class="m-0 font-weight-bold text-primary">🏢 Property Registry</h6>
                        <span class="badge bg-info">
                            <?php echo count($properties); ?>
                            <?php echo (count($properties) == 1) ? 'property' : 'properties'; ?> found
                        </span>
                    </div>
                    <div class="card-body">
                        <?php if (empty($properties)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-search fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">No properties found</h5>
                                <p class="text-muted">
                                    <?php if (!empty($filter_type) || !empty($filter_status) || !empty($search_term)): ?>
                                        No properties match your current filters. Try adjusting your search criteria.
                                    <?php else: ?>
                                        No properties have been added yet. Click "Add New Property" to get started.
                                    <?php endif; ?>
                                </p>
                                <?php if (!empty($filter_type) || !empty($filter_status) || !empty($search_term)): ?>
                                    <a href="properties.php" class="btn btn-outline-primary">
                                        <i class="fas fa-times"></i> Clear Filters
                                    </a>
                                <?php endif; ?>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover table-bordered-columns" id="propertiesTable" width="100%" cellspacing="0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>🖼️ Image</th>
                                            <th>🔢 ID</th>
                                            <th>🏢 Property Name</th>
                                            <th>📋 Type</th>
                                            <th>📍 Location</th>
                                            <th>📅 Acquisition Date</th>
                                            <th>💰 Cost</th>
                                            <th>🚦 Status</th>
                                            <th>⚙️ Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($properties as $property): ?>
                                        <tr>
                                        <td>
                                            <?php if (!empty($property['property_image'])): ?>
                                                <img src="../../uploads/properties/<?php echo $property['property_image']; ?>" 
                                                     class="img-thumbnail" alt="Property Image" style="max-height: 50px;">
                                            <?php else: ?>
                                                <i class="fas fa-building text-secondary" style="font-size: 1.5rem;"></i>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo $property['property_id']; ?></td>
                                        <td><?php echo $property['property_name']; ?></td>
                                        <td><?php echo $property['property_type']; ?></td>
                                        <td><?php echo $property['location']; ?></td>
                                        <td><?php echo date('M d, Y', strtotime($property['acquisition_date'])); ?></td>
                                        <td>₱<?php echo number_format($property['acquisition_cost'], 2); ?></td>
                                        <td>
                                            <?php if ($property['status'] == 'Available'): ?>
                                                <span class="badge bg-success">Available</span>
                                            <?php elseif ($property['status'] == 'Under Maintenance'): ?>
                                                <span class="badge bg-warning text-dark">Under Maintenance</span>
                                            <?php elseif ($property['status'] == 'Disposed'): ?>
                                                <span class="badge bg-danger">Disposed</span>
                                            <?php elseif ($property['status'] == 'Borrowed'): ?>
                                                <span class="badge bg-info">Borrowed</span>
                                            <?php else: ?>
                                                <span class="badge bg-secondary"><?php echo $property['status']; ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="action-buttons">
                                                <button type="button" class="btn btn-info btn-sm view-property" 
                                                    data-id="<?php echo $property['property_id']; ?>"
                                                    data-name="<?php echo htmlspecialchars($property['property_name']); ?>"
                                                    data-type="<?php echo htmlspecialchars($property['property_type']); ?>"
                                                    data-location="<?php echo htmlspecialchars($property['location']); ?>"
                                                    data-date="<?php echo date('F d, Y', strtotime($property['acquisition_date'])); ?>"
                                                    data-cost="<?php echo number_format($property['acquisition_cost'], 2); ?>"
                                                    data-status="<?php echo $property['status']; ?>"
                                                    data-description="<?php echo htmlspecialchars($property['description'] ?: 'No description available'); ?>"
                                                    data-remarks="<?php echo htmlspecialchars($property['remarks'] ?: 'No remarks available'); ?>"
                                                    data-image="<?php echo !empty($property['property_image']) ? '../../uploads/properties/' . $property['property_image'] : ''; ?>">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <?php if ($_SESSION['user_type'] == 'Admin'): ?>
                                                <button type="button" class="btn btn-primary btn-sm edit-property"
                                                    data-id="<?php echo $property['property_id']; ?>">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" class="btn btn-danger btn-sm delete-property" 
                                                    data-id="<?php echo $property['property_id']; ?>"
                                                    data-name="<?php echo htmlspecialchars($property['property_name']); ?>"
                                                    data-cost="<?php echo number_format($property['acquisition_cost'], 2); ?>">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                                <?php endif; ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Toast container -->
    <div class="toast-container position-fixed top-0 end-0 p-3">
        <div id="successToast" class="toast" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex align-items-center p-3">
                <div class="me-2">✅</div>
                <div class="toast-message me-auto"></div>
                <div>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Property Modal -->
    <div class="modal fade" id="addPropertyModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">🏢 Add New Property</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="post" enctype="multipart/form-data">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="property_name" class="form-label">🏢 Property Name</label>
                                    <input type="text" class="form-control" id="property_name" name="property_name" required>
                                </div>
                                <div class="mb-3">
                                    <label for="property_type" class="form-label">📋 Property Type</label>
                                    <select class="form-select" id="property_type" name="property_type" required>
                                        <option value="">--Select Property Type--</option>
                                        <option value="Building">🏢 Building</option>
                                        <option value="Land">🏞️ Land</option>
                                        <option value="Vehicle">🚗 Vehicle</option>
                                        <option value="Equipment">🔧 Equipment</option>
                                        <option value="Furniture">🪑 Furniture</option>
                                        <option value="Electronics">💻 Electronics</option>
                                        <option value="Other">📦 Other</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="description" class="form-label">📝 Description</label>
                                    <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="location" class="form-label">📍 Location</label>
                                    <input type="text" class="form-control" id="location" name="location" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="acquisition_date" class="form-label">📅 Acquisition Date</label>
                                    <input type="date" class="form-control" id="acquisition_date" name="acquisition_date" required>
                                </div>
                                <div class="mb-3">
                                    <label for="acquisition_cost" class="form-label">💰 Acquisition Cost</label>
                                    <div class="input-group">
                                        <span class="input-group-text">₱</span>
                                        <input type="number" class="form-control" id="acquisition_cost" name="acquisition_cost" step="0.01" min="0" required>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="status" class="form-label">🚦 Status</label>
                                    <select class="form-select" id="status" name="status" required>
                                        <option value="">--Select Status--</option>
                                        <option value="Available">✅ Available</option>
                                        <option value="Under Maintenance">🔧 Under Maintenance</option>
                                        <option value="Disposed">🗑️ Disposed</option>
                                        <option value="Borrowed">📤 Borrowed</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="property_image" class="form-label">🖼️ Property Image</label>
                                    <input type="file" class="form-control" id="property_image" name="property_image">
                                    <div class="form-text">Upload an image of the property (JPG, PNG)</div>
                                </div>
                                <div class="mb-3">
                                    <label for="remarks" class="form-label">💬 Remarks</label>
                                    <textarea class="form-control" id="remarks" name="remarks" rows="3"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="add_property" class="btn btn-primary">
                            💾 Save Property
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- View Property Modal -->
    <div class="modal fade" id="viewPropertyModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-info text-white">
                    <h5 class="modal-title">👁️ Property Details</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row mb-3">
                        <div class="col-md-8">
                            <h5 class="fw-bold" id="view_property_name"></h5>
                        </div>
                        <div class="col-md-4 text-end">
                            <span class="badge bg-secondary" id="view_property_type"></span>
                            <span class="badge" id="view_property_status"></span>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div id="property_image_container" class="col-md-5 mb-3 d-none">
                            <div class="card h-100">
                                <div class="card-header bg-light">
                                    <h6 class="mb-0">🖼️ Property Image</h6>
                                </div>
                                <div class="card-body text-center">
                                    <img id="view_property_image" src="" class="img-fluid rounded" alt="Property Image">
                                </div>
                            </div>
                        </div>
                        <div id="property_details_full" class="col-md-12">
                            <div id="property_details_partial" class="col-md-7">
                                <!-- Property Information -->
                                <div class="card mb-3">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">📋 Basic Information</h6>
                                    </div>
                                    <div class="card-body">
                                        <p><i class="fas fa-hashtag me-2 text-secondary"></i> <span class="fw-bold">🔢 Property ID:</span> <span id="view_property_id"></span></p>
                                        <p><i class="fas fa-map-marker-alt me-2 text-primary"></i> <span class="fw-bold">📍 Location:</span> <span id="view_location"></span></p>
                                        <p><i class="fas fa-calendar-alt me-2 text-success"></i> <span class="fw-bold">📅 Acquisition Date:</span> <span id="view_acquisition_date"></span></p>
                                        <p><i class="fas fa-money-bill-wave me-2 text-danger"></i> <span class="fw-bold">💰 Acquisition Cost:</span> <span id="view_acquisition_cost"></span></p>
                                    </div>
                                </div>
                                
                                <div id="additional_details_container" class="card d-none">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">📝 Additional Details</h6>
                                    </div>
                                    <div class="card-body">
                                        <div id="description_container" class="d-none">
                                            <p><i class="fas fa-info-circle me-2 text-primary"></i> <span class="fw-bold">📝 Description:</span></p>
                                            <p class="ps-4 mb-3" id="view_description"></p>
                                        </div>
                                        
                                        <div id="remarks_container" class="d-none">
                                            <p><i class="fas fa-comment me-2 text-info"></i> <span class="fw-bold">💬 Remarks:</span></p>
                                            <p class="ps-4" id="view_remarks"></p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Property Modal -->
    <div class="modal fade" id="deletePropertyModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title">🗑️ Confirm Deletion</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i> 
                        Are you sure you want to delete this property record? This action cannot be undone.
                    </div>
                    <div class="mt-3">
                        <p><strong>🔢 Property ID:</strong> <span id="delete_property_id"></span></p>
                        <p><strong>🏢 Name:</strong> <span id="delete_property_name"></span></p>
                        <p><strong>💰 Value:</strong> <span id="delete_property_cost"></span></p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <a href="#" id="confirm_delete" class="btn btn-danger">
                        🗑️ Delete Property
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Property Modal -->
    <div class="modal fade" id="editPropertyModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title">✏️ Edit Property</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form method="post" enctype="multipart/form-data">
                    <input type="hidden" name="property_id" id="edit_property_id">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_property_name" class="form-label">🏢 Property Name</label>
                                    <input type="text" class="form-control" id="edit_property_name" name="property_name" required>
                                </div>
                                <div class="mb-3">
                                    <label for="edit_property_type" class="form-label">📋 Property Type</label>
                                    <select class="form-select" id="edit_property_type" name="property_type" required>
                                        <option value="">--Select Property Type--</option>
                                        <option value="Building">🏢 Building</option>
                                        <option value="Land">🏞️ Land</option>
                                        <option value="Vehicle">🚗 Vehicle</option>
                                        <option value="Equipment">🔧 Equipment</option>
                                        <option value="Furniture">🪑 Furniture</option>
                                        <option value="Electronics">💻 Electronics</option>
                                        <option value="Other">📦 Other</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="edit_description" class="form-label">📝 Description</label>
                                    <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                                </div>
                                <div class="mb-3">
                                    <label for="edit_location" class="form-label">📍 Location</label>
                                    <input type="text" class="form-control" id="edit_location" name="location" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="edit_acquisition_date" class="form-label">📅 Acquisition Date</label>
                                    <input type="date" class="form-control" id="edit_acquisition_date" name="acquisition_date" required>
                                </div>
                                <div class="mb-3">
                                    <label for="edit_acquisition_cost" class="form-label">💰 Acquisition Cost</label>
                                    <div class="input-group">
                                        <span class="input-group-text">₱</span>
                                        <input type="number" class="form-control" id="edit_acquisition_cost" name="acquisition_cost" step="0.01" min="0" required>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="edit_status" class="form-label">🚦 Status</label>
                                    <select class="form-select" id="edit_status" name="status" required>
                                        <option value="">--Select Status--</option>
                                        <option value="Available">✅ Available</option>
                                        <option value="Under Maintenance">🔧 Under Maintenance</option>
                                        <option value="Disposed">🗑️ Disposed</option>
                                        <option value="Borrowed">📤 Borrowed</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="edit_property_image" class="form-label">🖼️ Property Image</label>
                                    <div id="current_image_container" class="mb-2 d-none">
                                        <img id="current_property_image" src="" alt="Current Image" class="img-thumbnail" style="max-height: 100px;">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="remove_image" name="remove_image" value="1">
                                            <label class="form-check-label" for="remove_image">
                                                🗑️ Remove current image
                                            </label>
                                        </div>
                                    </div>
                                    <input type="file" class="form-control" id="edit_property_image" name="property_image">
                                    <div class="form-text">Upload a new image to replace the current one (JPG, PNG)</div>
                                </div>
                                <div class="mb-3">
                                    <label for="edit_remarks" class="form-label">💬 Remarks</label>
                                    <textarea class="form-control" id="edit_remarks" name="remarks" rows="3"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" name="edit_property" class="btn btn-warning">
                            ✏️ Update Property
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- DataTables -->
    <link rel="stylesheet" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script>
        $(document).ready(function() {
            // Initialize DataTable with advanced configuration
            $('#propertiesTable').DataTable({
                "paging": true,
                "ordering": true,
                "info": true,
                "searching": true,
                "columnDefs": [
                    { "orderable": false, "targets": 0 }, // Make image column not sortable
                    { "width": "60px", "targets": 0 }    // Set width for image column
                ],
                "responsive": true,
                "autoWidth": false
            });
            
            // View Property
            $(document).on('click', '.view-property', function() {
                const id = $(this).data('id');
                const name = $(this).data('name');
                const type = $(this).data('type');
                const location = $(this).data('location');
                const date = $(this).data('date');
                const cost = $(this).data('cost');
                const status = $(this).data('status');
                const description = $(this).data('description');
                const remarks = $(this).data('remarks');
                const image = $(this).data('image');
                
                // Set values in the modal
                $('#view_property_id').text(id);
                $('#view_property_name').text(name);
                $('#view_property_type').text(type);
                $('#view_location').text(location);
                $('#view_acquisition_date').text(date);
                $('#view_acquisition_cost').text('₱' + cost);
                
                // Set status badge
                let statusBadgeClass = 'bg-secondary';
                if (status === 'Available') statusBadgeClass = 'bg-success';
                else if (status === 'Under Maintenance') statusBadgeClass = 'bg-warning text-dark';
                else if (status === 'Disposed') statusBadgeClass = 'bg-danger';
                else if (status === 'Borrowed') statusBadgeClass = 'bg-info';
                
                $('#view_property_status').removeClass().addClass('badge ' + statusBadgeClass).text(status);
                
                // Handle description and remarks
                if (description && description !== 'No description available') {
                    $('#view_description').text(description);
                    $('#description_container').removeClass('d-none');
                    $('#additional_details_container').removeClass('d-none');
                } else {
                    $('#description_container').addClass('d-none');
                }
                
                if (remarks && remarks !== 'No remarks available') {
                    $('#view_remarks').text(remarks);
                    $('#remarks_container').removeClass('d-none');
                    $('#additional_details_container').removeClass('d-none');
                } else {
                    $('#remarks_container').addClass('d-none');
                }
                
                if (!description && !remarks) {
                    $('#additional_details_container').addClass('d-none');
                }
                
                // Handle property image
                if (image) {
                    $('#view_property_image').attr('src', image);
                    $('#property_image_container').removeClass('d-none');
                    $('#property_details_full').removeClass('col-md-12').addClass('col-md-7');
                    $('#property_details_partial').removeClass('col-md-7').addClass('col-md-12');
                } else {
                    $('#property_image_container').addClass('d-none');
                    $('#property_details_full').addClass('col-md-12').removeClass('col-md-7');
                    $('#property_details_partial').addClass('col-md-12').removeClass('col-md-7');
                }
                
                // Show the modal
                $('#viewPropertyModal').modal('show');
            });
            
            // Edit Property
            $(document).on('click', '.edit-property', function() {
                const id = $(this).data('id');
                
                // Fetch property details via AJAX
                $.ajax({
                    url: 'get_property.php?id=' + id,
                    type: 'GET',
                    dataType: 'json',
                    success: function(data) {
                        if (data) {
                            // Populate the edit form
                            $('#edit_property_id').val(data.property_id);
                            $('#edit_property_name').val(data.property_name);
                            $('#edit_property_type').val(data.property_type);
                            $('#edit_description').val(data.description);
                            $('#edit_location').val(data.location);
                            $('#edit_acquisition_date').val(data.acquisition_date);
                            $('#edit_acquisition_cost').val(data.acquisition_cost);
                            $('#edit_status').val(data.status);
                            $('#edit_remarks').val(data.remarks);
                            
                            // Handle property image
                            if (data.property_image) {
                                $('#current_property_image').attr('src', '../../uploads/properties/' + data.property_image);
                                $('#current_image_container').removeClass('d-none');
                            } else {
                                $('#current_image_container').addClass('d-none');
                            }
                            
                            // Show the modal
                            $('#editPropertyModal').modal('show');
                        } else {
                            showToast('Error: Unable to fetch property data', 'danger');
                        }
                    },
                    error: function() {
                        // If AJAX fails, try getting the data from the DOM attributes as a fallback
                        const $btn = $(this);
                        const propertyRow = $btn.closest('tr');
                        
                        // Pull data from the view button's data attributes in the same row
                        const $viewBtn = propertyRow.find('.view-property');
                        
                        $('#edit_property_id').val(id);
                        $('#edit_property_name').val($viewBtn.data('name'));
                        $('#edit_property_type').val($viewBtn.data('type'));
                        $('#edit_location').val($viewBtn.data('location'));
                        $('#edit_status').val($viewBtn.data('status'));
                        
                        // Show the modal
                        $('#editPropertyModal').modal('show');
                        
                        showToast('Some property details could not be loaded automatically. Please check before saving.', 'warning');
                    }
                });
            });
            
            // Delete Property
            $(document).on('click', '.delete-property', function() {
                const id = $(this).data('id');
                const name = $(this).data('name');
                const cost = $(this).data('cost');
                
                // Set values in the confirmation modal
                $('#delete_property_id').text(id);
                $('#delete_property_name').text(name);
                $('#delete_property_cost').text('₱' + cost);
                
                // Set the delete link
                $('#confirm_delete').attr('href', 'properties.php?delete=' + id);
                
                // Show the modal
                $('#deletePropertyModal').modal('show');
            });
            
            // Reset forms when modals are closed
            $('#editPropertyModal').on('hidden.bs.modal', function() {
                $(this).find('form')[0].reset();
                $('#current_image_container').addClass('d-none');
            });
            
            // Function to show toast notifications
            function showToast(message, type = 'info') {
                // Get toast element
                const toast = document.getElementById('successToast');
                if (!toast) return;
                
                // Update toast content
                document.querySelector('#successToast .toast-message').textContent = message;
                
                // Update toast styling based on message type
                if (type === 'success') {
                    toast.style.backgroundColor = '#28a745';
                    toast.style.color = 'white';
                } else if (type === 'danger' || type === 'error') {
                    toast.style.backgroundColor = '#dc3545';
                    toast.style.color = 'white';
                } else if (type === 'warning') {
                    toast.style.backgroundColor = '#ffc107';
                    toast.style.color = '#000';
                } else {
                    toast.style.backgroundColor = '#17a2b8';
                    toast.style.color = 'white';
                }
                
                // Show the toast
                const bsToast = new bootstrap.Toast(toast, {
                    delay: 5000
                });
                bsToast.show();
            }
            
            // Show toast messages for session alerts
            <?php if(isset($_SESSION['error'])): ?>
            showToast('<?php echo addslashes($_SESSION['error']); ?>', 'danger');
            <?php unset($_SESSION['error']); ?>
            <?php endif; ?>
            
            <?php if(isset($_SESSION['success'])): ?>
            showToast('<?php echo addslashes($_SESSION['success']); ?>', 'success');
            <?php unset($_SESSION['success']); ?>
            <?php endif; ?>
            
            <?php if(isset($_SESSION['message']) && isset($_SESSION['message_type'])): ?>
            showToast('<?php echo addslashes($_SESSION['message']); ?>', '<?php echo $_SESSION['message_type']; ?>');
            <?php unset($_SESSION['message']); unset($_SESSION['message_type']); ?>
            <?php endif; ?>
        });
    </script>
</body>
</html> 