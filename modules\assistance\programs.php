<?php
// Prevent caching to ensure we always see fresh content
header("Cache-Control: no-store, no-cache, must-revalidate, max-age=0");
header("Cache-Control: post-check=0, pre-check=0", false);
header("Pragma: no-cache");

session_start();
include '../../includes/config/database.php';
include '../../includes/functions/utility.php';

// Check if user is logged in, if not redirect to login page
if (!isset($_SESSION['user_id'])) {
    header("Location: ../../login.php");
    exit;
}

// Check for database connection errors
$db_error = isset($db_connection_error) && $db_connection_error;

// Initialize variables
$message = '';
$message_type = '';
$page_title = "Assistance Programs - Barangay Management System";

// Check if there are messages stored in the session
if (isset($_SESSION['message']) && isset($_SESSION['message_type'])) {
    $message = $_SESSION['message'];
    $message_type = $_SESSION['message_type'];
    // Clear the session messages to prevent them from displaying again
    unset($_SESSION['message']);
    unset($_SESSION['message_type']);
}

// Process Add Program Form
if (isset($_POST['add_program'])) {
    $program_name = $_POST['program_name'] ?? '';
    $program_type = $_POST['program_type'] ?? '';
    $description = $_POST['description'] ?? '';
    $budget = $_POST['budget'] ?? null;
    $start_date = $_POST['start_date'] ?? null;
    $end_date = $_POST['end_date'] ?? null;
    $beneficiary_criteria = $_POST['beneficiary_criteria'] ?? '';
    $status = $_POST['status'] ?? 'Active';
    $coordinator_id = !empty($_POST['coordinator_id']) ? $_POST['coordinator_id'] : null;

    if (empty($budget)) $budget = null;
    if (empty($start_date)) $start_date = null;
    if (empty($end_date)) $end_date = null;

    if (!$db_error) {
        try {
            // Check if assistance_programs table exists
            $stmt = $conn->prepare("SHOW TABLES LIKE 'assistance_programs'");
            $stmt->execute();
            $table_exists = $stmt->rowCount() > 0;
            
            if (!$table_exists) {
                // Create the table if it doesn't exist
                $create_table_query = "CREATE TABLE assistance_programs (
                    program_id INT AUTO_INCREMENT PRIMARY KEY,
                    program_name VARCHAR(255) NOT NULL,
                    program_type VARCHAR(100),
                    description TEXT,
                    budget DECIMAL(10,2),
                    start_date DATE,
                    end_date DATE,
                    beneficiary_criteria TEXT,
                    status ENUM('Active', 'Pending', 'Completed', 'Cancelled') DEFAULT 'Active',
                    coordinator_id INT,
                    date_added TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (coordinator_id) REFERENCES officials(official_id) ON DELETE SET NULL
                )";
                
                $conn->exec($create_table_query);
                
                // Log the table creation
                $action_type = "System Update";
                $action_details = "Created assistance_programs table";
                log_activity($conn, $_SESSION['user_id'], $action_type, $action_details, 'assistance');
            }
            
            // Insert the new program
            $query = "INSERT INTO assistance_programs (
                      program_name, program_type, description, budget, 
                      start_date, end_date, beneficiary_criteria, status, coordinator_id)
                      VALUES (
                      :program_name, :program_type, :description, :budget, 
                      :start_date, :end_date, :beneficiary_criteria, :status, :coordinator_id)";
            
            $stmt = $conn->prepare($query);
            $stmt->bindParam(':program_name', $program_name);
            $stmt->bindParam(':program_type', $program_type);
            $stmt->bindParam(':description', $description);
            $stmt->bindParam(':budget', $budget);
            $stmt->bindParam(':start_date', $start_date);
            $stmt->bindParam(':end_date', $end_date);
            $stmt->bindParam(':beneficiary_criteria', $beneficiary_criteria);
            $stmt->bindParam(':status', $status);
            $stmt->bindParam(':coordinator_id', $coordinator_id);
            $stmt->execute();
            
            // Log the activity
            $action_type = "Assistance";
            $action_details = "Added new assistance program: $program_name";
            log_activity($conn, $_SESSION['user_id'], $action_type, $action_details, 'assistance');
            
            $message = "Assistance program has been successfully added.";
            $message_type = "success";
            
            // Redirect to prevent form resubmission on page refresh
            $_SESSION['message'] = $message;
            $_SESSION['message_type'] = $message_type;
            header("Location: programs.php");
            exit();
        } catch(PDOException $e) {
            $message = "Error: " . $e->getMessage();
            $message_type = "danger";
        }
    }
}

// Get all assistance programs
$programs = array();
$officials = array();
$total_programs = 0;
$active_count = 0;
$completed_count = 0;
$pending_count = 0;

if (!$db_error) {
    try {
        // Check if assistance_programs table exists
        $stmt = $conn->prepare("SHOW TABLES LIKE 'assistance_programs'");
        $stmt->execute();
        $table_exists = $stmt->rowCount() > 0;
        
        if ($table_exists) {
            // Simplified query to fetch all programs
            $query = "SELECT * FROM assistance_programs ORDER BY date_added DESC";
            
            $stmt = $conn->query($query);
            if ($stmt) {
                $programs = $stmt->fetchAll(PDO::FETCH_ASSOC);
                $total_programs = count($programs);
                
                // Get coordinator info separately if we have programs
                if (!empty($programs)) {
                    $coordinator_ids = array_filter(array_column($programs, 'coordinator_id'));
                    
                    if (!empty($coordinator_ids)) {
                        // Get officials info in one query
                        $officials_list = [];
                        
                        $placeholders = rtrim(str_repeat('?,', count($coordinator_ids)), ',');
                        $officials_query = "SELECT o.official_id, 
                                         CONCAT(r.first_name, ' ', r.last_name) as full_name,
                                         o.position
                                         FROM officials o 
                                         LEFT JOIN residents r ON o.resident_id = r.resident_id
                                         WHERE o.official_id IN ($placeholders)";
                        
                        $officials_stmt = $conn->prepare($officials_query);
                        $officials_stmt->execute(array_values($coordinator_ids));
                        
                        if ($officials_stmt) {
                            while ($official = $officials_stmt->fetch(PDO::FETCH_ASSOC)) {
                                $officials_list[$official['official_id']] = $official['full_name'] . ' - ' . $official['position'];
                            }
                        }
                    }
                    
                    // Add coordinator names to programs
                    foreach ($programs as &$program) {
                        if (!empty($program['coordinator_id']) && isset($officials_list[$program['coordinator_id']])) {
                            $program['coordinator_name'] = $officials_list[$program['coordinator_id']];
                        } else {
                            $program['coordinator_name'] = 'Not assigned';
                        }
                    }
                    unset($program); // Remove the reference to the last element
                }
                
                // Calculate statistics
                $active_count = 0;
                $completed_count = 0;
                $pending_count = 0;
                
                foreach ($programs as $program) {
                    if ($program['status'] == 'Active') {
                        $active_count++;
                    } elseif ($program['status'] == 'Completed') {
                        $completed_count++;
                    } elseif ($program['status'] == 'Pending') {
                        $pending_count++;
                    }
                }
            }
            
            // Get officials for coordinator dropdown
            $officials_query = "SELECT o.official_id, r.first_name, r.last_name, o.position 
                               FROM officials o 
                               LEFT JOIN residents r ON o.resident_id = r.resident_id
                               WHERE o.status = 'Active'
                               ORDER BY o.position, r.last_name";
            $officials_stmt = $conn->query($officials_query);
            if ($officials_stmt) {
                $officials = $officials_stmt->fetchAll(PDO::FETCH_ASSOC);
            }
        }
    } catch(PDOException $e) {
        error_log("Error retrieving data: " . $e->getMessage());
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $page_title; ?></title>

    <!-- Favicon -->
    <link rel="icon" href="<?php echo get_favicon_url($conn, '../../'); ?>" type="image/png">

    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdn.datatables.net/1.13.4/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="../../assets/css/style.css">
    <style>
        .stat-card {
            border-radius: 0.75rem;
            box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
            transition: all 0.3s ease;
            border: none;
            position: relative;
            z-index: 1;
        }
        .stat-card:hover {
            transform: translateY(-5px) !important;
            box-shadow: 0 0.5rem 2rem 0 rgba(58, 59, 69, 0.2) !important;
            z-index: 2;
        }
        .stat-icon {
            font-size: 2.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 60px;
            width: 60px;
            border-radius: 50%;
            margin-bottom: 10px;
            transition: transform 0.3s ease;
        }
        .stat-card:hover .stat-icon {
            transform: scale(1.1);
        }
        
        /* Background colors with opacity */
        .bg-primary-soft { background-color: rgba(78, 115, 223, 0.1) !important; }
        .bg-success-soft { background-color: rgba(28, 200, 138, 0.1) !important; }
        .bg-info-soft { background-color: rgba(54, 185, 204, 0.1) !important; }
        .bg-warning-soft { background-color: rgba(246, 194, 62, 0.1) !important; }
        .bg-danger-soft { background-color: rgba(231, 74, 59, 0.1) !important; }
        .bg-secondary-soft { background-color: rgba(108, 117, 125, 0.1) !important; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <?php include '../../includes/sidebar.php'; ?>
            
            <!-- Main Content -->
            <main class="col-md-8 ms-sm-auto col-lg-10 px-md-4">
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">🎁 Assistance Programs</h1>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addProgramModal">
                        <i class="fas fa-plus"></i> Add New Program
                    </button>
                </div>

                <?php if (!empty($message)): ?>
                <div class="alert alert-<?php echo $message_type; ?> alert-dismissible fade show" role="alert">
                    <?php echo $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
                <?php endif; ?>

                <?php if ($db_error): ?>
                <div class="alert alert-danger">
                    <strong>⚠️ Database Error:</strong> Could not connect to the database. Please contact the system administrator.
                </div>
                <?php else: ?>
                
                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card shadow h-100">
                            <div class="card-body p-3">
                                <div class="row align-items-center">
                                    <div class="col-4">
                                        <div class="stat-icon bg-primary-soft text-primary">
                                            📋
                                        </div>
                                    </div>
                                    <div class="col-8 text-end">
                                        <h4 class="mt-0 mb-1"><?php echo $total_programs; ?></h4>
                                        <p class="mb-0 text-muted">Total Programs</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card shadow h-100">
                            <div class="card-body p-3">
                                <div class="row align-items-center">
                                    <div class="col-4">
                                        <div class="stat-icon bg-success-soft text-success">
                                            ✅
                                        </div>
                                    </div>
                                    <div class="col-8 text-end">
                                        <h4 class="mt-0 mb-1"><?php echo $active_count; ?></h4>
                                        <p class="mb-0 text-muted">Active Programs</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card shadow h-100">
                            <div class="card-body p-3">
                                <div class="row align-items-center">
                                    <div class="col-4">
                                        <div class="stat-icon bg-warning-soft text-warning">
                                            ⏳
                                        </div>
                                    </div>
                                    <div class="col-8 text-end">
                                        <h4 class="mt-0 mb-1"><?php echo $pending_count; ?></h4>
                                        <p class="mb-0 text-muted">Pending Programs</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="card stat-card shadow h-100">
                            <div class="card-body p-3">
                                <div class="row align-items-center">
                                    <div class="col-4">
                                        <div class="stat-icon bg-secondary-soft text-secondary">
                                            🏁
                                        </div>
                                    </div>
                                    <div class="col-8 text-end">
                                        <h4 class="mt-0 mb-1"><?php echo $completed_count; ?></h4>
                                        <p class="mb-0 text-muted">Completed Programs</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Programs Table -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">📋 Assistance Programs List</h6>
                    </div>
                    <div class="card-body">                      
                        <?php if (empty($programs)): ?>
                            <div class="alert alert-info">
                                📭 No assistance programs found. Click "Add New Program" to create one.
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-bordered table-hover" id="programsTable" width="100%" cellspacing="0">
                                    <thead>
                                        <tr>
                                            <th>🏷️ Program Name</th>
                                            <th>🔖 Type</th>
                                            <th>💰 Budget</th>
                                            <th>📅 Duration</th>
                                            <th>🔄 Status</th>
                                            <th>👤 Coordinator</th>
                                            <th>⚙️ Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($programs as $program): ?>
                                        <tr>
                                            <td><?php echo htmlspecialchars($program['program_name']); ?></td>
                                            <td>
                                                <?php 
                                                $typeIcon = '';
                                                switch($program['program_type']) {
                                                    case 'Educational': $typeIcon = '🎓'; break;
                                                    case 'Medical': $typeIcon = '🏥'; break;
                                                    case 'Financial': $typeIcon = '💵'; break;
                                                    case 'Relief': $typeIcon = '🧰'; break;
                                                    case 'Senior Citizen': $typeIcon = '👵'; break;
                                                    case 'PWD': $typeIcon = '♿'; break;
                                                    default: $typeIcon = '📋'; break;
                                                }
                                                echo $typeIcon . ' ' . htmlspecialchars($program['program_type']); 
                                                ?>
                                            </td>
                                            <td>
                                                <?php if ($program['budget']): ?>
                                                    ₱<?php echo number_format($program['budget'], 2); ?>
                                                <?php else: ?>
                                                    <span class="text-muted">Not specified</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if ($program['start_date'] && $program['end_date']): ?>
                                                    <?php echo date('M d, Y', strtotime($program['start_date'])); ?> - 
                                                    <?php echo date('M d, Y', strtotime($program['end_date'])); ?>
                                                <?php else: ?>
                                                    <span class="text-muted">Not specified</span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="text-center">
                                                <span class="badge bg-<?php 
                                                    echo ($program['status'] == 'Active') ? 'success' : 
                                                        (($program['status'] == 'Completed') ? 'succes' : 
                                                        (($program['status'] == 'Cancelled') ? 'danger' : 'warning')); 
                                                ?>">
                                                    <?php 
                                                    $statusIcon = '';
                                                    switch($program['status']) {
                                                        case 'Active': $statusIcon = '✅'; break;
                                                        case 'Completed': $statusIcon = '🏁'; break;
                                                        case 'Cancelled': $statusIcon = '❌'; break;
                                                        case 'Pending': $statusIcon = '⏳'; break;
                                                        default: $statusIcon = '⏳'; break;
                                                    }
                                                    echo $statusIcon . ' ' . $program['status']; 
                                                    ?>
                                                </span>
                                            </td>
                                            <td><?php echo htmlspecialchars($program['coordinator_name'] ?? 'Not assigned'); ?></td>
                                            <td class="text-center">
                                                <a href="recipients.php?program_id=<?php echo $program['program_id']; ?>" class="btn btn-sm btn-info btn-icon" title="View Recipients">
                                                    <i class="fas fa-users"></i>
                                                </a>
                                                <a href="edit_program.php?id=<?php echo $program['program_id']; ?>" class="btn btn-sm btn-primary btn-icon" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <button class="btn btn-sm btn-warning btn-icon statusBtn" data-bs-toggle="modal" data-bs-target="#updateStatusModal" 
                                                    data-id="<?php echo $program['program_id']; ?>" 
                                                    data-name="<?php echo htmlspecialchars($program['program_name']); ?>"
                                                    data-status="<?php echo htmlspecialchars($program['status']); ?>"
                                                    title="Change Status">
                                                    <i class="fas fa-exchange-alt"></i>
                                                </button>
                                                <button class="btn btn-sm btn-danger btn-icon" data-bs-toggle="modal" data-bs-target="#deleteProgramModal<?php echo $program['program_id']; ?>" title="Delete">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                <?php endif; ?>
            </main>
        </div>
    </div>

    <!-- Add Program Modal -->
    <div class="modal fade" id="addProgramModal" tabindex="-1" aria-labelledby="addProgramModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="addProgramModalLabel">🎁 Add New Assistance Program</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <form action="" method="post" class="needs-validation" novalidate>
                    <div class="modal-body">
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="program_name" class="form-label">🏷️ Program Name</label>
                                <input type="text" class="form-control" id="program_name" name="program_name" required>
                                <div class="invalid-feedback">
                                    Please provide a program name.
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label for="program_type" class="form-label">🔖 Program Type</label>
                                <select class="form-select" id="program_type" name="program_type" required>
                                    <option value="">-- Select Type --</option>
                                    <option value="Educational">🎓 Educational</option>
                                    <option value="Medical">🏥 Medical</option>
                                    <option value="Financial">💵 Financial</option>
                                    <option value="Relief">🧰 Relief</option>
                                    <option value="Senior Citizen">👵 Senior Citizen</option>
                                    <option value="PWD">♿ PWD</option>
                                    <option value="Other">📋 Other</option>
                                </select>
                                <div class="invalid-feedback">
                                    Please select a program type.
                                </div>
                            </div>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="budget" class="form-label">💰 Budget (₱)</label>
                                <input type="number" step="0.01" class="form-control" id="budget" name="budget">
                            </div>
                            <div class="col-md-4">
                                <label for="start_date" class="form-label">📅 Start Date</label>
                                <input type="date" class="form-control" id="start_date" name="start_date">
                            </div>
                            <div class="col-md-4">
                                <label for="end_date" class="form-label">📅 End Date</label>
                                <input type="date" class="form-control" id="end_date" name="end_date">
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="description" class="form-label">📝 Description</label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                        </div>
                        <div class="mb-3">
                            <label for="beneficiary_criteria" class="form-label">👥 Beneficiary Criteria</label>
                            <textarea class="form-control" id="beneficiary_criteria" name="beneficiary_criteria" rows="3"></textarea>
                        </div>
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="status" class="form-label">🔄 Status</label>
                                <select class="form-select" id="status" name="status" required>
                                    <option value="Active">✅ Active</option>
                                    <option value="Pending">⏳ Pending</option>
                                    <option value="Completed">🏁 Completed</option>
                                    <option value="Cancelled">❌ Cancelled</option>
                                </select>
                                <div class="invalid-feedback">
                                    Please select a status.
                                </div>
                            </div>
                            <div class="col-md-6">
                                <label for="coordinator_id" class="form-label">👤 Coordinator</label>
                                <select class="form-select" id="coordinator_id" name="coordinator_id">
                                    <option value="">-- Select Coordinator --</option>
                                    <?php foreach($officials as $official): ?>
                                    <option value="<?php echo $official['official_id']; ?>">
                                        <?php echo htmlspecialchars($official['last_name'] . ', ' . $official['first_name'] . ' - ' . $official['position']); ?>
                                    </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">❌ Cancel</button>
                        <button type="submit" name="add_program" class="btn btn-primary">💾 Save Program</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Delete Program Modal -->
    <?php foreach ($programs as $program): ?>
    <div class="modal fade" id="deleteProgramModal<?php echo $program['program_id']; ?>" tabindex="-1" aria-labelledby="deleteProgramModalLabel<?php echo $program['program_id']; ?>" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title" id="deleteProgramModalLabel<?php echo $program['program_id']; ?>">🗑️ Delete Program</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete the program <strong><?php echo htmlspecialchars($program['program_name']); ?></strong>?</p>
                    <p class="text-danger">⚠️ <strong>Warning:</strong> This will also delete all recipient records associated with this program.</p>
                </div>
                <form action="program_actions.php" method="post">
                    <input type="hidden" name="action" value="delete_program">
                    <input type="hidden" name="program_id" value="<?php echo $program['program_id']; ?>">
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">❌ Cancel</button>
                        <button type="submit" class="btn btn-danger">🗑️ Delete Program</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <?php endforeach; ?>

    <!-- Update Status Modal -->
    <div class="modal fade" id="updateStatusModal" tabindex="-1" aria-labelledby="updateStatusModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-warning text-dark">
                    <h5 class="modal-title" id="updateStatusModalLabel">🔄 Update Program Status</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Update status for program: <strong id="programNameToUpdate"></strong></p>
                    <form action="program_actions.php" method="post" id="updateStatusForm">
                        <input type="hidden" name="action" value="update_status">
                        <input type="hidden" name="program_id" id="programIdToUpdate">
                        <div class="mb-3">
                            <label for="new_status" class="form-label">🔄 New Status</label>
                            <select class="form-select" id="new_status" name="status" required>
                                <option value="Active">✅ Active</option>
                                <option value="Pending">⏳ Pending</option>
                                <option value="Completed">🏁 Completed</option>
                                <option value="Cancelled">❌ Cancelled</option>
                            </select>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">❌ Cancel</button>
                        <button type="submit" class="btn btn-warning">🔄 Update Status</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Include jQuery and Bootstrap JS -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.13.4/js/dataTables.bootstrap5.min.js"></script>
    
    <script>
        $(document).ready(function() {
            // Initialize DataTable if the table exists
            if ($('#programsTable').length > 0) {
                $('#programsTable').DataTable({
                    "pageLength": 10,
                    "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "All"]],
                    "language": {
                        "lengthMenu": "Show _MENU_ entries",
                        "info": "Showing _START_ to _END_ of _TOTAL_ entries",
                        "search": "Search:",
                        "paginate": {
                            "first": "First",
                            "last": "Last",
                            "next": "Next",
                            "previous": "Previous"
                        }
                    },
                    "columnDefs": [
                        { "orderable": false, "targets": 6 } // Disable sorting on actions column
                    ]
                });
            }
            
            // Form validation
            (function() {
                'use strict';
                window.addEventListener('load', function() {
                    // Fetch all the forms we want to apply custom Bootstrap validation styles to
                    var forms = document.getElementsByClassName('needs-validation');
                    // Loop over them and prevent submission
                    var validation = Array.prototype.filter.call(forms, function(form) {
                        form.addEventListener('submit', function(event) {
                            if (form.checkValidity() === false) {
                                event.preventDefault();
                                event.stopPropagation();
                            }
                            form.classList.add('was-validated');
                        }, false);
                    });
                }, false);
            })();
            
            // Set up delete modal
            $('.deleteBtn').click(function() {
                var programId = $(this).data('id');
                var programName = $(this).data('name');
                $('#programIdToDelete').val(programId);
                $('#programNameToDelete').text(programName);
            });
            
            // Set up status update modal
            $('.statusBtn').click(function() {
                var programId = $(this).data('id');
                var programName = $(this).data('name');
                var currentStatus = $(this).data('status');
                $('#programIdToUpdate').val(programId);
                $('#programNameToUpdate').text(programName);
                $('#new_status').val(currentStatus);
            });
            
            // Date validation for start and end dates
            $('#end_date').on('change', function() {
                const startDate = $('#start_date').val();
                const endDate = $(this).val();
                
                if (startDate && endDate && new Date(endDate) < new Date(startDate)) {
                    alert('End date cannot be earlier than start date.');
                    $(this).val('');
                }
            });
        });
    </script>
</body>
</html> 